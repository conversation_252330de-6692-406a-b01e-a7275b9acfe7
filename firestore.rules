rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user is admin
    function isAdmin() {
      return isAuthenticated() && 
             (request.auth.token.admin == true || 
              request.auth.token.role == 'admin' ||
              request.auth.token.email in ['<EMAIL>', '<EMAIL>']);
    }
    
    // Helper function to check if user owns the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.token.email == userId;
    }
    
    // Helper function to check if user can read their own data
    function canReadOwnData(userId) {
      return isAuthenticated() && 
             (request.auth.token.email == userId || isAdmin());
    }
    
    // Users collection - stores user profiles
    match /users/{userId} {
      // Users can read and write their own profile
      // Ad<PERSON> can read all profiles but not write them (except through server)
      allow read: if canReadOwnData(userId);
      allow write: if isOwner(userId);
      
      // Admin read access for dashboard
      allow read: if isAdmin();
    }
    
    // Assessments collection - stores assessment results
    match /assessments/{assessmentId} {
      // Users can read and write their own assessments
      // Admins can read all assessments
      allow read: if isAuthenticated() && 
                     (resource.data.userId == request.auth.token.email || isAdmin());
      allow write: if isAuthenticated() && 
                      resource.data.userId == request.auth.token.email;
      allow create: if isAuthenticated() && 
                       request.resource.data.userId == request.auth.token.email;
      
      // Admin read access for dashboard
      allow read: if isAdmin();
    }
    
    // Audit logs collection - admin actions only
    match /audit_logs/{logId} {
      // Only admins can read audit logs
      // Only server can write audit logs
      allow read: if isAdmin();
      allow write: if false; // Only server-side writes allowed
    }
    
    // Admin settings collection
    match /admin_settings/{settingId} {
      // Only admins can read/write admin settings
      allow read, write: if isAdmin();
    }
    
    // Course information collection (read-only for users)
    match /courses/{courseId} {
      // All authenticated users can read course information
      // Only admins can modify course information
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // System notifications collection
    match /notifications/{notificationId} {
      // Users can read notifications addressed to them
      // Admins can read all notifications and create new ones
      allow read: if isAuthenticated() && 
                     (resource.data.userId == request.auth.token.email || isAdmin());
      allow write: if isAdmin();
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Storage rules for file uploads
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /profile_images/{userId}/{allPaths=**} {
      allow read: if request.auth != null && 
                     (request.auth.uid == userId || 
                      request.auth.token.admin == true);
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Assessment attachments (certificates, etc.)
    match /assessment_attachments/{userId}/{allPaths=**} {
      allow read: if request.auth != null && 
                     (request.auth.uid == userId || 
                      request.auth.token.admin == true);
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin uploads
    match /admin_uploads/{allPaths=**} {
      allow read, write: if request.auth != null && 
                            request.auth.token.admin == true;
    }
    
    // Public assets (course materials, etc.)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
                      request.auth.token.admin == true;
    }
    
    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
