const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const bodyParser = require('body-parser');
const path = require('path');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { admin, db, auth } = require('./config/firebase-admin');
const aiAssistant = require('./services/ai-assistant');
const { SECURITY_CONFIG, AuditLogger, AccessControl } = require('./config/security');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize security components
const auditLogger = new AuditLogger(db);

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.gstatic.com", "https://apis.google.com"],
      scriptSrcAttr: ["'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      connectSrc: ["'self'", "https://identitytoolkit.googleapis.com", "https://firestore.googleapis.com", "https://securetoken.googleapis.com"]
    }
  },
  hsts: false, // Disable HSTS for development
  upgradeInsecureRequests: false // Disable upgrade insecure requests for development
}));

app.use(cors({
  origin: ['http://localhost:3001', 'http://127.0.0.1:3001'],
  credentials: true
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Debug middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Rate limiting for AI endpoints
const aiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 AI requests per windowMs
  message: 'Too many AI requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for admin endpoints
const adminRateLimit = rateLimit({
  windowMs: SECURITY_CONFIG.RATE_LIMITS.ADMIN_API.windowMs,
  max: SECURITY_CONFIG.RATE_LIMITS.ADMIN_API.max,
  message: SECURITY_CONFIG.RATE_LIMITS.ADMIN_API.message,
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for export endpoints
const exportRateLimit = rateLimit({
  windowMs: SECURITY_CONFIG.RATE_LIMITS.EXPORT.windowMs,
  max: SECURITY_CONFIG.RATE_LIMITS.EXPORT.max,
  message: SECURITY_CONFIG.RATE_LIMITS.EXPORT.message,
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for login endpoints
const loginRateLimit = rateLimit({
  windowMs: SECURITY_CONFIG.RATE_LIMITS.LOGIN.windowMs,
  max: SECURITY_CONFIG.RATE_LIMITS.LOGIN.max,
  message: SECURITY_CONFIG.RATE_LIMITS.LOGIN.message,
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware to verify Firebase token
const verifyToken = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Middleware to verify admin role
const verifyAdmin = async (req, res, next) => {
  try {
    // Use AccessControl utility for admin verification
    if (AccessControl.hasAdminRole(req.user)) {
      next();
    } else {
      // Log unauthorized access attempt
      await auditLogger.logEvent('UNAUTHORIZED_ACCESS_ATTEMPT', req.user.email, {
        userAgent: req.headers['user-agent'],
        endpoint: req.originalUrl
      }, req.ip);

      return res.status(403).json({ error: 'Admin access required' });
    }
  } catch (error) {
    console.error('Admin verification error:', error);
    return res.status(403).json({ error: 'Admin access verification failed' });
  }
};

// Audit logging middleware for admin actions
const auditLog = (action) => {
  return (req, res, next) => {
    const originalSend = res.send;
    res.send = function(data) {
      // Log admin action using AuditLogger
      auditLogger.logEvent(action, req.user.email, {
        userAgent: req.headers['user-agent'],
        endpoint: req.originalUrl,
        method: req.method,
        success: res.statusCode < 400,
        responseSize: data ? data.length : 0
      }, req.ip).catch(err => console.error('Audit log error:', err));

      originalSend.call(this, data);
    };
    next();
  };
};

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// User registration endpoint
app.post('/api/register', async (req, res) => {
  try {
    const { name, age, email, preferredLanguage, hasStudiedBefore, supportNeeds, userType } = req.body;
    
    // Store user profile in Firestore
    const userProfile = {
      name,
      age: parseInt(age),
      email,
      preferredLanguage,
      hasStudiedBefore: hasStudiedBefore === 'yes',
      supportNeeds: supportNeeds || '',
      userType: userType || 'student',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      assessmentStatus: 'not_started'
    };

    await db.collection('users').doc(email).set(userProfile);
    
    res.json({ success: true, message: 'User profile created successfully' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Failed to create user profile' });
  }
});

// Get user profile
app.get('/api/profile', verifyToken, async (req, res) => {
  try {
    const userDoc = await db.collection('users').doc(req.user.email).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User profile not found' });
    }
    
    res.json(userDoc.data());
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// Update user profile
app.put('/api/profile', verifyToken, async (req, res) => {
  try {
    const updates = req.body;
    updates.updatedAt = admin.firestore.FieldValue.serverTimestamp();
    
    await db.collection('users').doc(req.user.email).update(updates);
    
    res.json({ success: true, message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Assessment routes
app.post('/api/assessment/save', verifyToken, async (req, res) => {
  try {
    const assessmentData = req.body;
    assessmentData.userId = req.user.email;
    assessmentData.createdAt = admin.firestore.FieldValue.serverTimestamp();

    // Save assessment result
    const assessmentRef = await db.collection('assessments').add(assessmentData);

    // Update user profile with assessment completion
    await db.collection('users').doc(req.user.email).update({
      assessmentStatus: 'completed',
      lastAssessmentId: assessmentRef.id,
      lastAssessmentDate: admin.firestore.FieldValue.serverTimestamp(),
      recommendedCourse: assessmentData.finalRecommendation
    });

    res.json({ success: true, assessmentId: assessmentRef.id });
  } catch (error) {
    console.error('Assessment save error:', error);
    res.status(500).json({ error: 'Failed to save assessment results' });
  }
});

app.get('/api/assessment/results', verifyToken, async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments')
      .where('userId', '==', req.user.email)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (assessmentsSnapshot.empty) {
      return res.status(404).json({ error: 'No assessment results found' });
    }

    const latestAssessment = assessmentsSnapshot.docs[0].data();
    res.json(latestAssessment);
  } catch (error) {
    console.error('Assessment results fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessment results' });
  }
});

// AI Assistant routes
app.post('/api/ai/welcome', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { userProfile } = req.body;
    const welcomeMessage = await aiAssistant.generateWelcomeMessage(userProfile);

    res.json({
      success: true,
      message: welcomeMessage,
      type: 'welcome'
    });
  } catch (error) {
    console.error('AI Welcome Error:', error);
    res.status(500).json({
      error: 'Failed to generate welcome message',
      fallback: true
    });
  }
});

app.post('/api/ai/section-intro', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { sectionNumber, userProfile, previousPerformance } = req.body;
    const introMessage = await aiAssistant.generateSectionIntro(sectionNumber, userProfile, previousPerformance);

    res.json({
      success: true,
      message: introMessage,
      type: 'section-intro'
    });
  } catch (error) {
    console.error('AI Section Intro Error:', error);
    res.status(500).json({
      error: 'Failed to generate section introduction',
      fallback: true
    });
  }
});

app.post('/api/ai/encouragement', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { currentProgress, userProfile, performancePattern } = req.body;
    const encouragementMessage = await aiAssistant.generateEncouragement(currentProgress, userProfile, performancePattern);

    res.json({
      success: true,
      message: encouragementMessage,
      type: 'encouragement'
    });
  } catch (error) {
    console.error('AI Encouragement Error:', error);
    res.status(500).json({
      error: 'Failed to generate encouragement',
      fallback: true
    });
  }
});

app.post('/api/ai/clarify', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { questionText, userProfile } = req.body;
    const clarification = await aiAssistant.clarifyQuestion(questionText, userProfile);

    res.json({
      success: true,
      message: clarification,
      type: 'clarification'
    });
  } catch (error) {
    console.error('AI Clarification Error:', error);
    res.status(500).json({
      error: 'Failed to generate clarification',
      fallback: true
    });
  }
});

app.post('/api/ai/feedback', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { assessmentResults, userProfile } = req.body;
    const feedback = await aiAssistant.generateAssessmentFeedback(assessmentResults, userProfile);

    res.json({
      success: true,
      message: feedback,
      type: 'feedback'
    });
  } catch (error) {
    console.error('AI Feedback Error:', error);
    res.status(500).json({
      error: 'Failed to generate feedback',
      fallback: true
    });
  }
});

app.post('/api/ai/question', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { userQuestion, userProfile, assessmentContext } = req.body;
    const answer = await aiAssistant.answerUserQuestion(userQuestion, userProfile, assessmentContext);

    res.json({
      success: true,
      message: answer,
      type: 'user-question'
    });
  } catch (error) {
    console.error('AI User Question Error:', error);
    res.status(500).json({
      error: 'Failed to answer question',
      fallback: true
    });
  }
});

app.get('/api/ai/health', async (req, res) => {
  try {
    const isHealthy = await aiAssistant.healthCheck();
    res.json({
      healthy: isHealthy,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Admin routes with enhanced security and functionality
app.get('/api/admin/users', adminRateLimit, verifyToken, verifyAdmin, auditLog('VIEW_USERS'), async (req, res) => {
  try {
    const { page = 1, limit = 50, search = '', status = '', course = '', startDate = '', endDate = '' } = req.query;

    let query = db.collection('users');

    // Apply filters
    if (status && status !== 'all') {
      query = query.where('assessmentStatus', '==', status);
    }

    if (course && course !== 'all') {
      query = query.where('recommendedCourse', '==', course);
    }

    // Get all matching documents
    const usersSnapshot = await query.get();
    let users = [];

    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      // Convert timestamps to readable dates
      const user = {
        id: doc.id,
        ...userData,
        createdAt: userData.createdAt?.toDate?.() || userData.createdAt,
        lastAssessmentDate: userData.lastAssessmentDate?.toDate?.() || userData.lastAssessmentDate,
        updatedAt: userData.updatedAt?.toDate?.() || userData.updatedAt
      };
      users.push(user);
    });

    // Apply client-side filtering for search and date ranges
    if (search) {
      const searchLower = search.toLowerCase();
      users = users.filter(user =>
        user.name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower) ||
        user.recommendedCourse?.toLowerCase().includes(searchLower)
      );
    }

    if (startDate) {
      const start = new Date(startDate);
      users = users.filter(user => {
        const userDate = user.createdAt ? new Date(user.createdAt) : null;
        return userDate && userDate >= start;
      });
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // End of day
      users = users.filter(user => {
        const userDate = user.createdAt ? new Date(user.createdAt) : null;
        return userDate && userDate <= end;
      });
    }

    // Sort by creation date (newest first)
    users.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
      return dateB - dateA;
    });

    // Pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = users.slice(startIndex, endIndex);

    // Calculate statistics
    const stats = {
      total: users.length,
      completed: users.filter(u => u.assessmentStatus === 'completed').length,
      inProgress: users.filter(u => u.assessmentStatus === 'in_progress').length,
      notStarted: users.filter(u => u.assessmentStatus === 'not_started').length
    };

    res.json({
      users: paginatedUsers,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(users.length / parseInt(limit)),
        totalUsers: users.length,
        limit: parseInt(limit)
      },
      stats
    });
  } catch (error) {
    console.error('Admin users fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

app.get('/api/admin/assessments', adminRateLimit, verifyToken, verifyAdmin, auditLog('VIEW_ASSESSMENTS'), async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments').get();
    const assessments = [];

    assessmentsSnapshot.forEach(doc => {
      const assessmentData = doc.data();
      assessments.push({
        id: doc.id,
        ...assessmentData,
        createdAt: assessmentData.createdAt?.toDate?.() || assessmentData.createdAt,
        completedAt: assessmentData.completedAt?.toDate?.() || assessmentData.completedAt
      });
    });

    // Sort by creation date (newest first)
    assessments.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
      return dateB - dateA;
    });

    res.json(assessments);
  } catch (error) {
    console.error('Admin assessments fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessments' });
  }
});

// Get detailed student information
app.get('/api/admin/student/:email', adminRateLimit, verifyToken, verifyAdmin, auditLog('VIEW_STUDENT_DETAILS'), async (req, res) => {
  try {
    const { email } = req.params;

    // Get user profile
    const userDoc = await db.collection('users').doc(email).get();
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'Student not found' });
    }

    const userData = userDoc.data();

    // Get student's assessments with detailed responses
    const assessmentsSnapshot = await db.collection('assessments')
      .where('userId', '==', email)
      .orderBy('createdAt', 'desc')
      .get();

    const assessments = [];
    assessmentsSnapshot.forEach(doc => {
      const assessmentData = doc.data();

      // Process assessment responses for better display
      const processedAssessment = {
        id: doc.id,
        ...assessmentData,
        createdAt: assessmentData.createdAt?.toDate?.() || assessmentData.createdAt,
        completedAt: assessmentData.completedAt?.toDate?.() || assessmentData.completedAt,
        // Group responses by section for better organization
        responsesBySection: assessmentData.responses ? groupResponsesBySection(assessmentData.responses) : {},
        // Calculate section scores
        sectionScores: assessmentData.responses ? calculateSectionScores(assessmentData.responses) : {},
        // Extract AI analysis details
        aiAnalysis: {
          summary: assessmentData.aiSummary || assessmentData.finalRecommendation || 'No AI analysis available',
          recommendation: assessmentData.finalRecommendation || 'No recommendation',
          confidence: assessmentData.confidenceScore || 'Not available',
          reasoning: assessmentData.aiReasoning || assessmentData.recommendationReasoning || 'No reasoning provided'
        }
      };

      assessments.push(processedAssessment);
    });

    const studentDetails = {
      profile: {
        ...userData,
        createdAt: userData.createdAt?.toDate?.() || userData.createdAt,
        lastAssessmentDate: userData.lastAssessmentDate?.toDate?.() || userData.lastAssessmentDate,
        updatedAt: userData.updatedAt?.toDate?.() || userData.updatedAt
      },
      assessments: assessments,
      totalAssessments: assessments.length,
      latestAssessment: assessments[0] || null
    };

    res.json(studentDetails);
  } catch (error) {
    console.error('Student details fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch student details' });
  }
});

// Helper function to group responses by assessment section
function groupResponsesBySection(responses) {
  const sections = {
    'Part 1: Basic IT Skills': [],
    'Part 2: Internet and Email': [],
    'Part 3: Advanced Digital Skills': []
  };

  if (Array.isArray(responses)) {
    responses.forEach((response, index) => {
      let sectionName = 'Part 1: Basic IT Skills';

      if (index >= 8 && index < 16) {
        sectionName = 'Part 2: Internet and Email';
      } else if (index >= 16) {
        sectionName = 'Part 3: Advanced Digital Skills';
      }

      sections[sectionName].push({
        questionIndex: index,
        question: response.question || `Question ${index + 1}`,
        answer: response.answer || 'No answer',
        timestamp: response.timestamp || null
      });
    });
  }

  return sections;
}

// Helper function to calculate section scores
function calculateSectionScores(responses) {
  const scores = {
    'Part 1: Basic IT Skills': { correct: 0, total: 0 },
    'Part 2: Internet and Email': { correct: 0, total: 0 },
    'Part 3: Advanced Digital Skills': { correct: 0, total: 0 }
  };

  if (Array.isArray(responses)) {
    responses.forEach((response, index) => {
      let sectionName = 'Part 1: Basic IT Skills';

      if (index >= 8 && index < 16) {
        sectionName = 'Part 2: Internet and Email';
      } else if (index >= 16) {
        sectionName = 'Part 3: Advanced Digital Skills';
      }

      scores[sectionName].total++;
      if (response.answer === 'yes' || response.answer === true) {
        scores[sectionName].correct++;
      }
    });
  }

  return scores;
}

// Export student data as CSV
app.get('/api/admin/export/students', exportRateLimit, verifyToken, verifyAdmin, auditLog('EXPORT_STUDENT_DATA'), async (req, res) => {
  try {
    const { format = 'csv', columns = 'all', includeAssessments = 'false', student = null } = req.query;

    let usersQuery = db.collection('users');

    // If exporting specific student
    if (student) {
      const userDoc = await db.collection('users').doc(student).get();
      if (!userDoc.exists) {
        return res.status(404).json({ error: 'Student not found' });
      }

      const userData = userDoc.data();
      const users = [{
        id: userDoc.id,
        ...userData,
        createdAt: userData.createdAt?.toDate?.() || userData.createdAt,
        lastAssessmentDate: userData.lastAssessmentDate?.toDate?.() || userData.lastAssessmentDate
      }];

      return exportUsersData(users, format, columns, includeAssessments === 'true', res, student);
    }

    // Export all users
    const usersSnapshot = await usersQuery.get();
    const users = [];

    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      users.push({
        id: doc.id,
        ...userData,
        createdAt: userData.createdAt?.toDate?.() || userData.createdAt,
        lastAssessmentDate: userData.lastAssessmentDate?.toDate?.() || userData.lastAssessmentDate
      });
    });

    exportUsersData(users, format, columns, includeAssessments === 'true', res);
  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({ error: 'Failed to export data' });
  }
});

// Helper function to export users data
async function exportUsersData(users, format, columns, includeAssessments, res, singleStudent = null) {
  if (format === 'csv') {
    // Define available columns
    const allColumns = {
      name: 'Full Name',
      email: 'Email Address',
      age: 'Age',
      preferredLanguage: 'Preferred Language',
      hasStudiedBefore: 'Previously Studied',
      supportNeeds: 'Support Needs',
      assessmentStatus: 'Assessment Status',
      recommendedCourse: 'Recommended Course',
      createdAt: 'Registration Date',
      lastAssessmentDate: 'Last Assessment Date'
    };

    // Add assessment columns if requested
    if (includeAssessments) {
      allColumns.totalQuestionsAnswered = 'Questions Answered';
      allColumns.timeSpent = 'Time Spent (minutes)';
      allColumns.aiSummary = 'AI Summary';
      allColumns.confidenceScore = 'Confidence Score';
    }

    // Determine which columns to include
    let selectedColumns = allColumns;
    if (columns !== 'all') {
      const columnList = columns.split(',');
      selectedColumns = {};
      columnList.forEach(col => {
        if (allColumns[col]) {
          selectedColumns[col] = allColumns[col];
        }
      });
    }

    // If including assessments, fetch assessment data
    if (includeAssessments) {
      for (let user of users) {
        try {
          const assessmentsSnapshot = await db.collection('assessments')
            .where('userId', '==', user.email)
            .orderBy('createdAt', 'desc')
            .limit(1)
            .get();

          if (!assessmentsSnapshot.empty) {
            const latestAssessment = assessmentsSnapshot.docs[0].data();
            user.totalQuestionsAnswered = latestAssessment.totalQuestionsAnswered || 0;
            user.timeSpent = latestAssessment.timeSpent || 0;
            user.aiSummary = latestAssessment.aiSummary || latestAssessment.finalRecommendation || '';
            user.confidenceScore = latestAssessment.confidenceScore || '';
          }
        } catch (error) {
          console.error('Error fetching assessment for user:', user.email, error);
        }
      }
    }

    // Generate CSV content
    const headers = Object.values(selectedColumns).join(',');
    const rows = users.map(user => {
      return Object.keys(selectedColumns).map(key => {
        let value = user[key] || '';

        // Format specific fields
        if (key === 'createdAt' || key === 'lastAssessmentDate') {
          value = value ? new Date(value).toLocaleDateString() : '';
        } else if (key === 'hasStudiedBefore') {
          value = value ? 'Yes' : 'No';
        }

        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          value = `"${value.replace(/"/g, '""')}"`;
        }

        return value;
      }).join(',');
    });

    const csvContent = [headers, ...rows].join('\n');

    const filename = singleStudent ?
      `student_${singleStudent.replace('@', '_')}_${new Date().toISOString().split('T')[0]}.csv` :
      `students_export_${new Date().toISOString().split('T')[0]}.csv`;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(csvContent);
  } else {
    res.json(users);
  }
}

// Get available export columns
app.get('/api/admin/export/columns', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const columns = {
      basic: {
        name: 'Full Name',
        email: 'Email Address',
        age: 'Age',
        preferredLanguage: 'Preferred Language',
        hasStudiedBefore: 'Previously Studied',
        supportNeeds: 'Support Needs',
        createdAt: 'Registration Date'
      },
      assessment: {
        assessmentStatus: 'Assessment Status',
        recommendedCourse: 'Recommended Course',
        lastAssessmentDate: 'Last Assessment Date',
        totalQuestionsAnswered: 'Questions Answered',
        timeSpent: 'Time Spent (minutes)',
        aiSummary: 'AI Summary',
        confidenceScore: 'Confidence Score'
      }
    };

    res.json(columns);
  } catch (error) {
    console.error('Error fetching export columns:', error);
    res.status(500).json({ error: 'Failed to fetch export columns' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Visit http://localhost:${PORT} to view the application`);
}).on('error', (err) => {
  console.error('Server error:', err);
});

module.exports = app;
