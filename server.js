const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const bodyParser = require('body-parser');
const path = require('path');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const { admin, db, auth } = require('./config/firebase-admin');
const aiAssistant = require('./services/ai-assistant');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.gstatic.com", "https://apis.google.com"],
      scriptSrcAttr: ["'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      connectSrc: ["'self'", "https://identitytoolkit.googleapis.com", "https://firestore.googleapis.com", "https://securetoken.googleapis.com"]
    }
  },
  hsts: false, // Disable HSTS for development
  upgradeInsecureRequests: false // Disable upgrade insecure requests for development
}));

app.use(cors({
  origin: ['http://localhost:3001', 'http://127.0.0.1:3001'],
  credentials: true
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Debug middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Rate limiting for AI endpoints
const aiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 AI requests per windowMs
  message: 'Too many AI requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware to verify Firebase token
const verifyToken = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'No token provided' });
  }

  try {
    const decodedToken = await auth.verifyIdToken(token);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// User registration endpoint
app.post('/api/register', async (req, res) => {
  try {
    const { name, age, email, preferredLanguage, hasStudiedBefore, supportNeeds, userType } = req.body;
    
    // Store user profile in Firestore
    const userProfile = {
      name,
      age: parseInt(age),
      email,
      preferredLanguage,
      hasStudiedBefore: hasStudiedBefore === 'yes',
      supportNeeds: supportNeeds || '',
      userType: userType || 'student',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      assessmentStatus: 'not_started'
    };

    await db.collection('users').doc(email).set(userProfile);
    
    res.json({ success: true, message: 'User profile created successfully' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Failed to create user profile' });
  }
});

// Get user profile
app.get('/api/profile', verifyToken, async (req, res) => {
  try {
    const userDoc = await db.collection('users').doc(req.user.email).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User profile not found' });
    }
    
    res.json(userDoc.data());
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// Update user profile
app.put('/api/profile', verifyToken, async (req, res) => {
  try {
    const updates = req.body;
    updates.updatedAt = admin.firestore.FieldValue.serverTimestamp();
    
    await db.collection('users').doc(req.user.email).update(updates);
    
    res.json({ success: true, message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Assessment routes
app.post('/api/assessment/save', verifyToken, async (req, res) => {
  try {
    const assessmentData = req.body;
    assessmentData.userId = req.user.email;
    assessmentData.createdAt = admin.firestore.FieldValue.serverTimestamp();

    // Save assessment result
    const assessmentRef = await db.collection('assessments').add(assessmentData);

    // Update user profile with assessment completion
    await db.collection('users').doc(req.user.email).update({
      assessmentStatus: 'completed',
      lastAssessmentId: assessmentRef.id,
      lastAssessmentDate: admin.firestore.FieldValue.serverTimestamp(),
      recommendedCourse: assessmentData.finalRecommendation
    });

    res.json({ success: true, assessmentId: assessmentRef.id });
  } catch (error) {
    console.error('Assessment save error:', error);
    res.status(500).json({ error: 'Failed to save assessment results' });
  }
});

app.get('/api/assessment/results', verifyToken, async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments')
      .where('userId', '==', req.user.email)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .get();

    if (assessmentsSnapshot.empty) {
      return res.status(404).json({ error: 'No assessment results found' });
    }

    const latestAssessment = assessmentsSnapshot.docs[0].data();
    res.json(latestAssessment);
  } catch (error) {
    console.error('Assessment results fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessment results' });
  }
});

// AI Assistant routes
app.post('/api/ai/welcome', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { userProfile } = req.body;
    const welcomeMessage = await aiAssistant.generateWelcomeMessage(userProfile);

    res.json({
      success: true,
      message: welcomeMessage,
      type: 'welcome'
    });
  } catch (error) {
    console.error('AI Welcome Error:', error);
    res.status(500).json({
      error: 'Failed to generate welcome message',
      fallback: true
    });
  }
});

app.post('/api/ai/section-intro', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { sectionNumber, userProfile, previousPerformance } = req.body;
    const introMessage = await aiAssistant.generateSectionIntro(sectionNumber, userProfile, previousPerformance);

    res.json({
      success: true,
      message: introMessage,
      type: 'section-intro'
    });
  } catch (error) {
    console.error('AI Section Intro Error:', error);
    res.status(500).json({
      error: 'Failed to generate section introduction',
      fallback: true
    });
  }
});

app.post('/api/ai/encouragement', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { currentProgress, userProfile, performancePattern } = req.body;
    const encouragementMessage = await aiAssistant.generateEncouragement(currentProgress, userProfile, performancePattern);

    res.json({
      success: true,
      message: encouragementMessage,
      type: 'encouragement'
    });
  } catch (error) {
    console.error('AI Encouragement Error:', error);
    res.status(500).json({
      error: 'Failed to generate encouragement',
      fallback: true
    });
  }
});

app.post('/api/ai/clarify', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { questionText, userProfile } = req.body;
    const clarification = await aiAssistant.clarifyQuestion(questionText, userProfile);

    res.json({
      success: true,
      message: clarification,
      type: 'clarification'
    });
  } catch (error) {
    console.error('AI Clarification Error:', error);
    res.status(500).json({
      error: 'Failed to generate clarification',
      fallback: true
    });
  }
});

app.post('/api/ai/feedback', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { assessmentResults, userProfile } = req.body;
    const feedback = await aiAssistant.generateAssessmentFeedback(assessmentResults, userProfile);

    res.json({
      success: true,
      message: feedback,
      type: 'feedback'
    });
  } catch (error) {
    console.error('AI Feedback Error:', error);
    res.status(500).json({
      error: 'Failed to generate feedback',
      fallback: true
    });
  }
});

app.post('/api/ai/question', aiRateLimit, verifyToken, async (req, res) => {
  try {
    const { userQuestion, userProfile, assessmentContext } = req.body;
    const answer = await aiAssistant.answerUserQuestion(userQuestion, userProfile, assessmentContext);

    res.json({
      success: true,
      message: answer,
      type: 'user-question'
    });
  } catch (error) {
    console.error('AI User Question Error:', error);
    res.status(500).json({
      error: 'Failed to answer question',
      fallback: true
    });
  }
});

app.get('/api/ai/health', async (req, res) => {
  try {
    const isHealthy = await aiAssistant.healthCheck();
    res.json({
      healthy: isHealthy,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      healthy: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Admin routes
app.get('/api/admin/users', verifyToken, async (req, res) => {
  try {
    // Check if user is admin (you can implement admin role checking here)
    const usersSnapshot = await db.collection('users').get();
    const users = [];

    usersSnapshot.forEach(doc => {
      users.push({ id: doc.id, ...doc.data() });
    });

    res.json(users);
  } catch (error) {
    console.error('Admin users fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

app.get('/api/admin/assessments', verifyToken, async (req, res) => {
  try {
    const assessmentsSnapshot = await db.collection('assessments').get();
    const assessments = [];

    assessmentsSnapshot.forEach(doc => {
      assessments.push({ id: doc.id, ...doc.data() });
    });

    res.json(assessments);
  } catch (error) {
    console.error('Admin assessments fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch assessments' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Visit http://localhost:${PORT} to view the application`);
}).on('error', (err) => {
  console.error('Server error:', err);
});

module.exports = app;
