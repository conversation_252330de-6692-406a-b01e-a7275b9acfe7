<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Digital Skills Assessment</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin-dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- Skip to main content link for keyboard users -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner"></div>
        <p>Loading dashboard...</p>
    </div>

    <!-- Admin Header -->
    <header class="admin-header" role="banner">
        <div class="admin-header-content">
            <div class="admin-logo">
                <h1>Digital Skills Assessment</h1>
                <span class="admin-badge" aria-label="Current page: Admin Dashboard">Admin Dashboard</span>
            </div>
            <div class="admin-user-info">
                <span class="admin-user-name" id="adminUserName" aria-label="Current admin user">Admin User</span>
                <button class="btn btn-outline btn-sm" id="adminSignOutBtn" aria-label="Sign out of admin dashboard">Sign Out</button>
            </div>
        </div>
    </header>

    <!-- Admin Navigation -->
    <nav class="admin-nav" id="adminNav" role="navigation" aria-label="Admin dashboard navigation">
        <div class="nav-content">
            <button class="nav-item active" data-section="overview" aria-pressed="true" aria-describedby="overview-desc">
                <span class="nav-icon" aria-hidden="true">📊</span>
                <span class="nav-text">Overview</span>
            </button>
            <button class="nav-item" data-section="students" aria-pressed="false" aria-describedby="students-desc">
                <span class="nav-icon" aria-hidden="true">👥</span>
                <span class="nav-text">Students</span>
            </button>
            <button class="nav-item" data-section="assessments" aria-pressed="false" aria-describedby="assessments-desc">
                <span class="nav-icon" aria-hidden="true">📝</span>
                <span class="nav-text">Assessments</span>
            </button>
            <button class="nav-item" data-section="reports" aria-pressed="false" aria-describedby="reports-desc">
                <span class="nav-icon" aria-hidden="true">📈</span>
                <span class="nav-text">Reports</span>
            </button>
        </div>
        <!-- Hidden descriptions for screen readers -->
        <div class="sr-only">
            <div id="overview-desc">View dashboard overview and statistics</div>
            <div id="students-desc">Manage student data and assessments</div>
            <div id="assessments-desc">View detailed assessment data</div>
            <div id="reports-desc">Generate reports and analytics</div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main" role="main" aria-label="Admin dashboard main content" id="main-content">
        <!-- Overview Section -->
        <section id="overviewSection" class="admin-section active" role="region" aria-labelledby="overview-heading">
            <div class="section-header">
                <h2 id="overview-heading">Dashboard Overview</h2>
                <p>Quick insights into your assessment system</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3>Total Students</h3>
                        <span class="stat-number" id="totalStudentsCount">0</span>
                        <span class="stat-change positive" id="studentsChange">+0 this week</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3>Completed Assessments</h3>
                        <span class="stat-number" id="completedCount">0</span>
                        <span class="stat-change" id="completedChange">0% completion rate</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <h3>In Progress</h3>
                        <span class="stat-number" id="inProgressCount">0</span>
                        <span class="stat-change" id="inProgressChange">0 active today</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-content">
                        <h3>Not Started</h3>
                        <span class="stat-number" id="notStartedCount">0</span>
                        <span class="stat-change" id="notStartedChange">0 pending</span>
                    </div>
                </div>
            </div>

            <div class="overview-charts">
                <div class="chart-card">
                    <h3>Recent Activity</h3>
                    <div class="activity-list" id="recentActivity">
                        <div class="activity-item">
                            <span class="activity-time">Loading...</span>
                            <span class="activity-text">Fetching recent activity</span>
                        </div>
                    </div>
                </div>
                <div class="chart-card">
                    <h3>Course Recommendations</h3>
                    <div class="recommendations-chart" id="recommendationsChart">
                        <div class="chart-loading">Loading chart data...</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Students Section -->
        <section id="studentsSection" class="admin-section">
            <div class="section-header">
                <h2>Student Management</h2>
                <p>View and manage all student assessments</p>
            </div>

            <!-- Search and Filters -->
            <div class="filters-container" role="search" aria-label="Student search and filtering">
                <div class="search-box">
                    <label for="studentSearch" class="sr-only">Search students by name or email</label>
                    <input type="text" id="studentSearch" placeholder="Search students by name or email..." class="search-input" aria-describedby="search-help">
                    <button class="search-btn" id="searchBtn" aria-label="Execute search">
                        <span aria-hidden="true">🔍</span>
                    </button>
                    <div id="search-help" class="sr-only">Enter student name or email to filter results</div>
                </div>

                <div class="filters-row" role="group" aria-label="Filter options">
                    <label for="statusFilter" class="sr-only">Filter by assessment status</label>
                    <select id="statusFilter" class="filter-select" aria-label="Assessment status filter">
                        <option value="all">All Status</option>
                        <option value="completed">Completed</option>
                        <option value="in_progress">In Progress</option>
                        <option value="not_started">Not Started</option>
                    </select>

                    <label for="courseFilter" class="sr-only">Filter by recommended course</label>
                    <select id="courseFilter" class="filter-select" aria-label="Course recommendation filter">
                        <option value="all">All Courses</option>
                        <option value="Computer Skills – Beginners">Computer Skills – Beginners</option>
                        <option value="Computer Skills – Intermediate">Computer Skills – Intermediate</option>
                        <option value="Computer Skills – Advanced">Computer Skills – Advanced</option>
                    </select>

                    <label for="startDateFilter" class="sr-only">Filter by start date</label>
                    <input type="date" id="startDateFilter" class="filter-input" aria-label="Start date filter">

                    <label for="endDateFilter" class="sr-only">Filter by end date</label>
                    <input type="date" id="endDateFilter" class="filter-input" aria-label="End date filter">

                    <button class="btn btn-outline btn-sm" id="clearFiltersBtn" aria-label="Clear all filters">Clear Filters</button>
                    <button class="btn btn-primary btn-sm" id="exportStudentsBtn" aria-label="Export student data">Export CSV</button>
                </div>
            </div>

            <!-- Students Table -->
            <div class="table-container" role="region" aria-label="Students data table" tabindex="0">
                <table class="admin-table" id="studentsTable" role="table" aria-label="Student assessment data">
                    <thead>
                        <tr role="row">
                            <th class="sortable" data-sort="name" role="columnheader" aria-sort="none" tabindex="0" aria-label="Full Name, sortable column">
                                Full Name <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="sortable" data-sort="email" role="columnheader" aria-sort="none" tabindex="0" aria-label="Email, sortable column">
                                Email <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="sortable" data-sort="createdAt" role="columnheader" aria-sort="none" tabindex="0" aria-label="Start Date, sortable column">
                                Start Date <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="sortable" data-sort="lastAssessmentDate" role="columnheader" aria-sort="none" tabindex="0" aria-label="Completion Date, sortable column">
                                Completion Date <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="sortable" data-sort="assessmentStatus" role="columnheader" aria-sort="none" tabindex="0" aria-label="Status, sortable column">
                                Status <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th class="sortable" data-sort="recommendedCourse" role="columnheader" aria-sort="none" tabindex="0" aria-label="Recommended Course, sortable column">
                                Recommended Course <span class="sort-indicator" aria-hidden="true"></span>
                            </th>
                            <th role="columnheader">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="studentsTableBody" role="rowgroup">
                        <tr class="loading-row" role="row">
                            <td colspan="7" role="cell">
                                <div class="table-loading" aria-live="polite">
                                    <div class="spinner-sm" aria-hidden="true"></div>
                                    <span>Loading students...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="paginationInfo">Showing 0 of 0 students</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-outline btn-sm" id="prevPageBtn" disabled>← Previous</button>
                    <div class="page-numbers" id="pageNumbers"></div>
                    <button class="btn btn-outline btn-sm" id="nextPageBtn" disabled>Next →</button>
                </div>
            </div>
        </section>

        <!-- Assessments Section -->
        <section id="assessmentsSection" class="admin-section">
            <div class="section-header">
                <h2>Assessment Data</h2>
                <p>Detailed view of all assessment responses</p>
            </div>
            <div class="coming-soon">
                <h3>Assessment Analytics Coming Soon</h3>
                <p>Detailed assessment analytics and reporting features will be available here.</p>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reportsSection" class="admin-section">
            <div class="section-header">
                <h2>Reports & Analytics</h2>
                <p>Generate comprehensive reports and insights</p>
            </div>
            <div class="coming-soon">
                <h3>Advanced Reporting Coming Soon</h3>
                <p>Comprehensive reporting and analytics dashboard will be available here.</p>
            </div>
        </section>
    </main>

    <!-- Student Detail Modal -->
    <div id="studentDetailModal" class="modal-overlay">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>Student Details</h3>
                <button class="modal-close" id="closeStudentModal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="studentDetailContent">
                    <div class="detail-loading">
                        <div class="spinner"></div>
                        <span>Loading student details...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Customization Modal -->
    <div id="exportModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Customize Export</h3>
                <button class="modal-close" id="closeExportModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <div class="export-section">
                        <h4>Export Format</h4>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="exportFormat" value="csv" checked>
                                <span>CSV (Comma Separated Values)</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="exportFormat" value="json">
                                <span>JSON (JavaScript Object Notation)</span>
                            </label>
                        </div>
                    </div>

                    <div class="export-section">
                        <h4>Include Assessment Data</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="includeAssessments">
                                <span>Include detailed assessment responses and AI analysis</span>
                            </label>
                        </div>
                    </div>

                    <div class="export-section">
                        <h4>Select Columns</h4>
                        <div class="columns-selection">
                            <div class="column-group">
                                <h5>Basic Information</h5>
                                <div class="checkbox-group" id="basicColumns">
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="name" checked>
                                        <span>Full Name</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="email" checked>
                                        <span>Email Address</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="age">
                                        <span>Age</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="preferredLanguage">
                                        <span>Preferred Language</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="hasStudiedBefore">
                                        <span>Previously Studied</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="supportNeeds">
                                        <span>Support Needs</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="createdAt" checked>
                                        <span>Registration Date</span>
                                    </label>
                                </div>
                            </div>

                            <div class="column-group">
                                <h5>Assessment Information</h5>
                                <div class="checkbox-group" id="assessmentColumns">
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="assessmentStatus" checked>
                                        <span>Assessment Status</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="recommendedCourse" checked>
                                        <span>Recommended Course</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="lastAssessmentDate">
                                        <span>Last Assessment Date</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="totalQuestionsAnswered">
                                        <span>Questions Answered</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="timeSpent">
                                        <span>Time Spent (minutes)</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" value="aiSummary">
                                        <span>AI Summary</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="column-actions">
                            <button class="btn btn-outline btn-sm" id="selectAllColumns">Select All</button>
                            <button class="btn btn-outline btn-sm" id="selectNoneColumns">Select None</button>
                            <button class="btn btn-outline btn-sm" id="selectDefaultColumns">Default Selection</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="cancelExport">Cancel</button>
                <button class="btn btn-primary" id="confirmExport">Export Data</button>
            </div>
        </div>
    </div>

    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    
    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/admin-dashboard.js"></script>
</body>
</html>
