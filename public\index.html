<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Skills Assessment</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" style="width: 0%"></div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <header class="header">
            <h1 class="logo">Digital Skills Assessment</h1>
            <p class="subtitle">Discover your digital skills and get personalized course recommendations</p>
        </header>

        <main class="main-content">
            <!-- Landing Page -->
            <div id="landingPage" class="page active">
                <div class="welcome-card">
                    <h2>Welcome!</h2>
                    <p>Choose how you'd like to proceed:</p>
                    
                    <div class="button-group">
                        <button class="btn btn-primary" id="startAssessmentBtn">
                            <span class="btn-icon">🎓</span>
                            Start Assessment
                            <span class="btn-subtitle">For students and learners</span>
                        </button>

                        <button class="btn btn-secondary" id="adminLoginBtn">
                            <span class="btn-icon">👨‍💼</span>
                            Admin Login
                            <span class="btn-subtitle">For teachers and administrators</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Student Options -->
            <div id="studentOptions" class="page">
                <div class="options-card">
                    <h2>Student Portal</h2>
                    <p>Are you a new or returning student?</p>
                    
                    <div class="button-group">
                        <button class="btn btn-primary" id="newStudentBtn">
                            <span class="btn-icon">✨</span>
                            New Student
                            <span class="btn-subtitle">Create account and start assessment</span>
                        </button>

                        <button class="btn btn-outline" id="returningStudentBtn">
                            <span class="btn-icon">🔑</span>
                            Returning Student
                            <span class="btn-subtitle">Continue your assessment</span>
                        </button>
                    </div>

                    <button class="btn-link" id="backToMainBtn">← Back to main menu</button>
                </div>
            </div>

            <!-- Student Registration -->
            <div id="registrationPage" class="page">
                <div class="form-card">
                    <h2>Create Your Account</h2>
                    <p>Tell us a bit about yourself to get started</p>
                    
                    <form id="registrationForm" class="form">
                        <div class="form-step active" data-step="1">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="age">Age *</label>
                                <input type="number" id="age" name="age" min="13" max="100" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">Password *</label>
                                <input type="password" id="password" name="password" required minlength="6">
                                <small>At least 6 characters</small>
                            </div>
                            
                            <button type="button" class="btn btn-primary" id="nextStepBtn">
                                Next Step →
                            </button>
                        </div>
                        
                        <div class="form-step" data-step="2">
                            <div class="form-group">
                                <label for="preferredLanguage">Preferred Language *</label>
                                <select id="preferredLanguage" name="preferredLanguage" required>
                                    <option value="">Select language</option>
                                    <option value="english">English</option>
                                    <option value="spanish">Spanish</option>
                                    <option value="french">French</option>
                                    <option value="german">German</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Have you studied with us before? *</label>
                                <div class="radio-group">
                                    <label class="radio-label">
                                        <input type="radio" name="hasStudiedBefore" value="yes" required>
                                        <span>Yes</span>
                                    </label>
                                    <label class="radio-label">
                                        <input type="radio" name="hasStudiedBefore" value="no" required>
                                        <span>No</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="supportNeeds">Any disability or support needs? (Optional)</label>
                                <textarea id="supportNeeds" name="supportNeeds" rows="3" 
                                         placeholder="Please describe any accommodations you might need..."></textarea>
                            </div>
                            
                            <div class="button-group">
                                <button type="button" class="btn btn-outline" id="prevStepBtn">
                                    ← Previous
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    Create Account
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <button class="btn-link" id="backToStudentOptionsBtn1">← Back to student options</button>
                </div>
            </div>

            <!-- Student Login -->
            <div id="studentLoginPage" class="page">
                <div class="form-card">
                    <h2>Welcome Back!</h2>
                    <p>Sign in to continue your assessment</p>

                    <form id="studentLoginForm" class="form">
                        <div class="form-group">
                            <label for="loginEmail">Email Address</label>
                            <input type="email" id="loginEmail" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="loginPassword">Password</label>
                            <input type="password" id="loginPassword" name="password" required>
                        </div>

                        <button type="submit" class="btn btn-primary">Sign In</button>
                    </form>

                    <div class="form-links">
                        <a href="#" id="signUpLink">Don't have an account? Sign up</a>
                    </div>

                    <button class="btn-link" id="backToStudentOptionsBtn2">← Back to student options</button>
                </div>
            </div>

            <!-- Admin Login -->
            <div id="adminLoginPage" class="page">
                <div class="form-card">
                    <h2>Admin Login</h2>
                    <p>Access the administrative dashboard</p>
                    
                    <form id="adminLoginForm" class="form">
                        <div class="form-group">
                            <label for="adminEmail">Admin Email</label>
                            <input type="email" id="adminEmail" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="adminPassword">Password</label>
                            <input type="password" id="adminPassword" name="password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Admin Sign In</button>
                    </form>
                    
                    <button class="btn-link" id="backToMainMenuBtn">← Back to main menu</button>
                </div>
            </div>

            <!-- Student Dashboard -->
            <div id="studentDashboard" class="page">
                <div class="dashboard-card">
                    <h2>Welcome back, <span id="studentName"></span>!</h2>

                    <div class="dashboard-content">
                        <div class="status-card">
                            <h3>Assessment Status</h3>
                            <p id="assessmentStatus">Not started</p>
                            <div class="progress-indicator">
                                <div class="progress-circle">
                                    <span id="progressPercent">0%</span>
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" id="continueAssessmentBtn">
                                Start Assessment
                            </button>
                            <button class="btn btn-outline" id="viewResultsBtn" style="display: none;">
                                View Results
                            </button>
                        </div>
                    </div>

                    <button class="btn-link" id="signOutBtn1">Sign Out</button>
                </div>
            </div>

            <!-- Admin Dashboard -->
            <div id="adminDashboard" class="page">
                <div class="dashboard-card">
                    <h2>Admin Dashboard</h2>

                    <div class="admin-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <h3>Total Students</h3>
                                <span class="stat-number" id="totalStudents">0</span>
                            </div>
                            <div class="stat-card">
                                <h3>Active Assessments</h3>
                                <span class="stat-number" id="activeAssessments">0</span>
                            </div>
                            <div class="stat-card">
                                <h3>Completed</h3>
                                <span class="stat-number" id="completedAssessments">0</span>
                            </div>
                        </div>

                        <div class="admin-actions">
                            <button class="btn btn-primary" id="viewAllStudentsBtn">View All Students</button>
                            <button class="btn btn-outline" id="exportDataBtn">Export Data</button>
                        </div>
                    </div>

                    <button class="btn-link" id="signOutBtn2">Sign Out</button>
                </div>
            </div>

            <!-- Assessment Welcome -->
            <div id="assessmentWelcome" class="page">
                <div class="assessment-card">
                    <div class="ai-assistant">
                        <div class="ai-avatar">🤖</div>
                        <div class="ai-message">
                            <h2 id="aiWelcomeTitle">Hello! I'm your Digital Skills Assistant</h2>
                            <div id="aiWelcomeMessage">
                                <p>I'm here to help you discover your digital skills level. Please answer honestly - there are no wrong answers!</p>
                                <p>This assessment will help us recommend the perfect course for you.</p>
                            </div>
                            <div class="ai-loading" id="aiWelcomeLoading" style="display: none;">
                                <div class="ai-spinner"></div>
                                <span>Personalizing your experience...</span>
                            </div>
                        </div>
                    </div>

                    <div class="assessment-info">
                        <div class="info-item">
                            <span class="info-icon">⏱️</span>
                            <span>Estimated time: 10-15 minutes</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">📝</span>
                            <span>Up to 38 questions in 3 sections</span>
                        </div>
                        <div class="info-item">
                            <span class="info-icon">✨</span>
                            <span>Personalized course recommendations</span>
                        </div>
                    </div>

                    <div class="assessment-actions">
                        <button class="btn btn-primary btn-large" id="startAssessmentMainBtn">
                            Start Assessment
                        </button>
                        <button class="btn-link" id="backToDashboardBtn">← Back to Dashboard</button>
                    </div>
                </div>
            </div>

            <!-- Assessment Question -->
            <div id="assessmentQuestion" class="page">
                <div class="question-card">
                    <div class="question-header">
                        <div class="question-progress">
                            <span id="questionCounter">Question 1 of 8</span>
                            <div class="progress-bar-question">
                                <div class="progress-fill-question" id="questionProgressFill"></div>
                            </div>
                        </div>
                        <div class="section-indicator" id="sectionIndicator">Part 1: Basic IT Skills</div>
                    </div>

                    <div class="question-content">
                        <h2 id="questionText">I can turn on my phone, tablet or computer.</h2>
                        <button class="btn-help" id="needHelpBtn">
                            <span class="help-icon">❓</span>
                            Need Help?
                        </button>
                    </div>

                    <div class="answer-buttons">
                        <button class="btn btn-answer btn-yes" id="answerYes">
                            <span class="answer-icon">✅</span>
                            Yes, I can do this
                        </button>
                        <button class="btn btn-answer btn-no" id="answerNo">
                            <span class="answer-icon">❌</span>
                            No, I cannot do this
                        </button>
                    </div>

                    <div class="question-navigation">
                        <button class="btn btn-outline" id="prevQuestionBtn" style="display: none;">
                            ← Previous
                        </button>
                        <button class="btn-link" id="skipQuestionBtn">Skip for now</button>
                    </div>
                </div>
            </div>

            <!-- Section Transition -->
            <div id="sectionTransition" class="page">
                <div class="transition-card">
                    <div class="ai-assistant">
                        <div class="ai-avatar">🤖</div>
                        <div class="ai-message">
                            <h2 id="transitionTitle">Well done!</h2>
                            <p id="transitionMessage">You're doing great. Ready to try the next section?</p>
                        </div>
                    </div>

                    <div class="section-completion">
                        <div class="completion-animation">🎉</div>
                        <div class="completion-stats">
                            <span id="sectionScore">Section completed!</span>
                        </div>
                    </div>

                    <div class="transition-actions">
                        <button class="btn btn-primary btn-large" id="continueToNextSection">
                            Continue to Next Section
                        </button>
                        <button class="btn btn-outline" id="viewCurrentResults">
                            View My Results
                        </button>
                    </div>
                </div>
            </div>

            <!-- Assessment Results -->
            <div id="assessmentResults" class="page">
                <div class="results-card">
                    <div class="ai-assistant">
                        <div class="ai-avatar">🤖</div>
                        <div class="ai-message">
                            <h2 id="resultsTitle">Excellent work!</h2>
                            <p id="resultsMessage">You've completed the assessment. Here's what I recommend for you:</p>
                        </div>
                    </div>

                    <div class="recommendation-card">
                        <h3 id="recommendedCourse">Computer Skills – Beginners</h3>
                        <p id="courseDescription">This course is perfect for building your foundational digital skills.</p>

                        <div class="results-summary">
                            <div class="result-item">
                                <span class="result-label">Questions Answered:</span>
                                <span class="result-value" id="totalAnswered">8</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Time Spent:</span>
                                <span class="result-value" id="timeSpent">5 minutes</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Assessment Date:</span>
                                <span class="result-value" id="assessmentDate">Today</span>
                            </div>
                        </div>
                    </div>

                    <div class="results-actions">
                        <button class="btn btn-primary" id="retakeAssessmentBtn">
                            Retake Assessment
                        </button>
                        <button class="btn btn-outline" id="backToDashboardFromResults">
                            Back to Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Question Clarification Modal -->
    <div id="clarificationModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Question Help</h3>
                <button class="modal-close" id="closeClarificationModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="question-reminder">
                    <strong>Question:</strong>
                    <p id="clarificationQuestionText"></p>
                </div>
                <div class="ai-clarification">
                    <div class="ai-assistant-small">
                        <div class="ai-avatar-small">🤖</div>
                        <div class="ai-explanation">
                            <div id="clarificationContent">
                                <div class="ai-loading">
                                    <div class="ai-spinner"></div>
                                    <span>Getting help for you...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="gotItBtn">Got it, thanks!</button>
            </div>
        </div>
    </div>

    <!-- Interactive AI Assistant -->
    <div id="aiAssistantFloat" class="ai-float" style="display: none;">
        <button class="ai-float-btn" id="aiFloatBtn">
            <span class="ai-float-icon">🤖</span>
            <span class="ai-float-text">Ask AI</span>
        </button>
        <div class="ai-chat" id="aiChat" style="display: none;">
            <div class="ai-chat-header">
                <span>AI Assistant</span>
                <button class="ai-chat-close" id="aiChatClose">&times;</button>
            </div>
            <div class="ai-chat-messages" id="aiChatMessages">
                <div class="ai-message-item ai-message">
                    <div class="ai-avatar-tiny">🤖</div>
                    <div class="message-content">Hi! I'm here to help with any questions about the assessment.</div>
                </div>
            </div>
            <div class="ai-chat-input">
                <input type="text" id="aiChatInput" placeholder="Type your question...">
                <button id="aiChatSend">Send</button>
            </div>
        </div>
    </div>

    <!-- Firebase SDKs -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    
    <!-- App Scripts -->
    <script src="js/firebase-config.js"></script>
    <script src="js/ai-client.js"></script>
    <script src="js/assessment.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
