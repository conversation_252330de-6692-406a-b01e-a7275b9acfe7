const OpenAI = require('openai');
require('dotenv').config();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuration
const AI_CONFIG = {
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  max_tokens: 150,
  timeout: 5000, // 5 seconds
};

// System prompt for the AI assistant
const SYSTEM_PROMPT = `You are a friendly, supportive digital skills assessment assistant. Your role is to guide learners through their digital skills journey with encouragement and clear explanations. 

Key guidelines:
- Always use simple, jargon-free language
- Be positive and constructive
- Adapt your communication style to the user's background and needs
- Keep responses to 2-3 sentences maximum
- Use encouraging, growth-mindset language
- Avoid technical jargon unless necessary
- Include specific, actionable advice when appropriate
- Maintain a consistent supportive tone

Remember: You're helping people discover and build their digital skills, not testing them. Focus on encouragement and learning.`;

// Fallback messages for when AI is unavailable
const FALLBACK_MESSAGES = {
  welcome: "Hello! I'm your Digital Skills Assistant. I'm here to help you discover your digital skills level. Please answer honestly - there are no wrong answers!",
  encouragement: "You're doing great! Keep going - every question helps us understand your skills better.",
  sectionTransition: "Well done on completing that section! Ready to continue with the next part?",
  clarification: "This question is asking about your ability to perform a specific digital task. Answer 'Yes' if you can do it confidently, or 'No' if you're not sure or haven't done it before.",
  feedback: "Thank you for completing the assessment! Based on your responses, we've identified the perfect course to help you develop your digital skills further."
};

// Helper function to create user context for prompts
function createUserContext(userProfile, additionalContext = {}) {
  const context = {
    name: userProfile.name || 'there',
    age: userProfile.age || null,
    language: userProfile.preferredLanguage || 'english',
    hasStudiedBefore: userProfile.hasStudiedBefore || false,
    supportNeeds: userProfile.supportNeeds || null,
    ...additionalContext
  };

  let contextString = `User: ${context.name}`;
  if (context.age) contextString += `, age ${context.age}`;
  if (context.language !== 'english') contextString += `, prefers ${context.language}`;
  if (context.hasStudiedBefore) contextString += `, has studied with us before`;
  if (context.supportNeeds) contextString += `, accessibility needs: ${context.supportNeeds}`;

  return contextString;
}

// Main AI service functions
class AIAssistantService {
  
  // Generate personalized welcome message
  async generateWelcomeMessage(userProfile) {
    try {
      const userContext = createUserContext(userProfile);
      
      const prompt = `${userContext}

Generate a warm, personalized welcome message for this user starting their digital skills assessment. Include:
- Personal greeting using their name
- Brief explanation of the assessment purpose
- Encouragement that there are no wrong answers
- Mention the estimated time (10-15 minutes)

Adapt the tone and language complexity based on their background.`;

      const response = await this.callOpenAI(prompt);
      return response || FALLBACK_MESSAGES.welcome;
      
    } catch (error) {
      console.error('AI Welcome Message Error:', error);
      return FALLBACK_MESSAGES.welcome;
    }
  }

  // Generate section introduction messages
  async generateSectionIntro(sectionNumber, userProfile, previousPerformance = null) {
    try {
      const userContext = createUserContext(userProfile);
      const sectionNames = {
        1: 'Basic IT Skills (Entry Level)',
        2: 'Intermediate Digital Skills (Improver Level)', 
        3: 'Confident User Skills (Level 1)'
      };

      let performanceContext = '';
      if (previousPerformance) {
        performanceContext = `Previous section performance: ${previousPerformance.score}/${previousPerformance.total} questions answered "Yes".`;
      }

      const prompt = `${userContext}
${performanceContext}

Generate an encouraging introduction for ${sectionNames[sectionNumber]}. Explain what this section tests and provide appropriate encouragement. Keep it brief and motivating.`;

      const response = await this.callOpenAI(prompt);
      return response || FALLBACK_MESSAGES.sectionTransition;
      
    } catch (error) {
      console.error('AI Section Intro Error:', error);
      return FALLBACK_MESSAGES.sectionTransition;
    }
  }

  // Generate progressive encouragement during assessment
  async generateEncouragement(currentProgress, userProfile, performancePattern = null) {
    try {
      const userContext = createUserContext(userProfile);
      
      let progressContext = `Progress: Question ${currentProgress.currentQuestion} of ${currentProgress.totalQuestions} in ${currentProgress.sectionName}`;
      
      if (performancePattern) {
        progressContext += `. Recent answers: ${performancePattern.recentAnswers.join(', ')}`;
      }

      const prompt = `${userContext}
${progressContext}

Generate a brief, encouraging message to motivate the user. Acknowledge their progress and provide positive reinforcement. Be specific about their progress.`;

      const response = await this.callOpenAI(prompt);
      return response || FALLBACK_MESSAGES.encouragement;
      
    } catch (error) {
      console.error('AI Encouragement Error:', error);
      return FALLBACK_MESSAGES.encouragement;
    }
  }

  // Clarify questions for users who need help
  async clarifyQuestion(questionText, userProfile) {
    try {
      const userContext = createUserContext(userProfile);

      const prompt = `${userContext}

Question: "${questionText}"

Provide a simple, clear explanation of what this question is asking. Use examples or analogies if helpful. Don't give the answer, just clarify what the skill means in everyday terms.`;

      const response = await this.callOpenAI(prompt);
      return response || FALLBACK_MESSAGES.clarification;
      
    } catch (error) {
      console.error('AI Clarification Error:', error);
      return FALLBACK_MESSAGES.clarification;
    }
  }

  // Generate comprehensive assessment feedback
  async generateAssessmentFeedback(assessmentResults, userProfile) {
    try {
      const userContext = createUserContext(userProfile);
      
      const resultsContext = `
Assessment Results:
- Part 1 (Basic IT): ${assessmentResults.part1Score}/8 questions answered "Yes"
- Part 2 (Intermediate): ${assessmentResults.part2Score}/16 questions answered "Yes" 
- Part 3 (Advanced): ${assessmentResults.part3Score}/14 questions answered "Yes"
- Total questions answered: ${assessmentResults.totalQuestionsAnswered}
- Time spent: ${assessmentResults.timeSpent} minutes
- Recommended course: ${assessmentResults.finalRecommendation}`;

      const prompt = `${userContext}
${resultsContext}

Generate personalized feedback including:
1. Acknowledge their effort and completion
2. Highlight 2-3 specific strengths based on their answers
3. Explain why the recommended course fits their needs
4. Provide 2-3 encouraging next steps for their learning journey

Be positive, specific, and motivating.`;

      const response = await this.callOpenAI(prompt);
      return response || FALLBACK_MESSAGES.feedback;
      
    } catch (error) {
      console.error('AI Feedback Error:', error);
      return FALLBACK_MESSAGES.feedback;
    }
  }

  // Answer general user questions during assessment
  async answerUserQuestion(userQuestion, userProfile, assessmentContext = {}) {
    try {
      const userContext = createUserContext(userProfile, assessmentContext);

      const prompt = `${userContext}

User question: "${userQuestion}"

Provide a helpful, encouraging response. If it's about the assessment process, explain clearly. If it's about a technical concept, use simple terms. Always be supportive and guide them back to the assessment when appropriate.`;

      const response = await this.callOpenAI(prompt);
      return response || "I'm here to help! Feel free to continue with your assessment, and remember there are no wrong answers.";
      
    } catch (error) {
      console.error('AI User Question Error:', error);
      return "I'm here to help! Feel free to continue with your assessment, and remember there are no wrong answers.";
    }
  }

  // Core OpenAI API call with error handling and timeout
  async callOpenAI(prompt) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), AI_CONFIG.timeout);

    try {
      const completion = await openai.chat.completions.create({
        model: AI_CONFIG.model,
        messages: [
          { role: 'system', content: SYSTEM_PROMPT },
          { role: 'user', content: prompt }
        ],
        temperature: AI_CONFIG.temperature,
        max_tokens: AI_CONFIG.max_tokens,
      }, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (completion.choices && completion.choices[0] && completion.choices[0].message) {
        return completion.choices[0].message.content.trim();
      }
      
      throw new Error('Invalid response format from OpenAI');
      
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        console.error('OpenAI request timeout');
      } else {
        console.error('OpenAI API Error:', error.message);
      }
      
      throw error;
    }
  }

  // Health check for AI service
  async healthCheck() {
    try {
      const response = await this.callOpenAI('Respond with "AI service is working" if you can see this message.');
      return response.includes('working');
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
module.exports = new AIAssistantService();
