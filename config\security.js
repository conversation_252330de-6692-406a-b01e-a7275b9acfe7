// Security configuration and utilities for the admin dashboard

const crypto = require('crypto');

// Security configuration
const SECURITY_CONFIG = {
    // Rate limiting
    RATE_LIMITS: {
        ADMIN_API: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 100, // limit each IP to 100 requests per windowMs
            message: 'Too many admin API requests, please try again later.'
        },
        EXPORT: {
            windowMs: 60 * 60 * 1000, // 1 hour
            max: 10, // limit exports to 10 per hour
            message: 'Export limit exceeded, please try again later.'
        },
        LOGIN: {
            windowMs: 15 * 60 * 1000, // 15 minutes
            max: 5, // limit login attempts
            message: 'Too many login attempts, please try again later.'
        }
    },
    
    // Data encryption
    ENCRYPTION: {
        ALGORITHM: 'aes-256-gcm',
        KEY_LENGTH: 32,
        IV_LENGTH: 16,
        TAG_LENGTH: 16
    },
    
    // Admin access control
    ADMIN_EMAILS: [
        '<EMAIL>',
        '<EMAIL>'
        // Add more admin emails as needed
    ],
    
    // Audit logging
    AUDIT_EVENTS: {
        LOGIN: 'admin_login',
        LOGOUT: 'admin_logout',
        VIEW_USERS: 'view_users',
        VIEW_STUDENT_DETAILS: 'view_student_details',
        EXPORT_DATA: 'export_data',
        MODIFY_SETTINGS: 'modify_settings',
        DELETE_DATA: 'delete_data'
    },
    
    // Data retention
    DATA_RETENTION: {
        AUDIT_LOGS_DAYS: 365, // Keep audit logs for 1 year
        SESSION_TIMEOUT_MINUTES: 60, // Admin session timeout
        EXPORT_CACHE_HOURS: 24 // Cache export data for 24 hours
    }
};

// Encryption utilities
class DataEncryption {
    constructor() {
        this.key = process.env.ENCRYPTION_KEY || crypto.randomBytes(SECURITY_CONFIG.ENCRYPTION.KEY_LENGTH);
    }
    
    encrypt(text) {
        try {
            const iv = crypto.randomBytes(SECURITY_CONFIG.ENCRYPTION.IV_LENGTH);
            const cipher = crypto.createCipher(SECURITY_CONFIG.ENCRYPTION.ALGORITHM, this.key);
            cipher.setAAD(Buffer.from('admin-dashboard', 'utf8'));
            
            let encrypted = cipher.update(text, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const tag = cipher.getAuthTag();
            
            return {
                encrypted: encrypted,
                iv: iv.toString('hex'),
                tag: tag.toString('hex')
            };
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Failed to encrypt data');
        }
    }
    
    decrypt(encryptedData) {
        try {
            const { encrypted, iv, tag } = encryptedData;
            const decipher = crypto.createDecipher(SECURITY_CONFIG.ENCRYPTION.ALGORITHM, this.key);
            decipher.setAAD(Buffer.from('admin-dashboard', 'utf8'));
            decipher.setAuthTag(Buffer.from(tag, 'hex'));
            
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Failed to decrypt data');
        }
    }
}

// Audit logging utilities
class AuditLogger {
    constructor(db) {
        this.db = db;
    }
    
    async logEvent(event, adminEmail, details = {}, ipAddress = null) {
        try {
            const logEntry = {
                event: event,
                adminEmail: adminEmail,
                timestamp: new Date(),
                ipAddress: ipAddress,
                userAgent: details.userAgent || null,
                details: details,
                sessionId: details.sessionId || null
            };
            
            // In production, store in Firestore
            if (process.env.NODE_ENV === 'production' && this.db) {
                await this.db.collection('audit_logs').add(logEntry);
            }
            
            // Always log to console for development
            console.log(`AUDIT: ${JSON.stringify(logEntry)}`);
            
            return logEntry;
        } catch (error) {
            console.error('Audit logging error:', error);
            // Don't throw error to avoid breaking the main operation
        }
    }
    
    async getAuditLogs(filters = {}) {
        try {
            if (!this.db) {
                return [];
            }
            
            let query = this.db.collection('audit_logs');
            
            // Apply filters
            if (filters.adminEmail) {
                query = query.where('adminEmail', '==', filters.adminEmail);
            }
            
            if (filters.event) {
                query = query.where('event', '==', filters.event);
            }
            
            if (filters.startDate) {
                query = query.where('timestamp', '>=', filters.startDate);
            }
            
            if (filters.endDate) {
                query = query.where('timestamp', '<=', filters.endDate);
            }
            
            // Order by timestamp (newest first)
            query = query.orderBy('timestamp', 'desc');
            
            // Limit results
            if (filters.limit) {
                query = query.limit(filters.limit);
            }
            
            const snapshot = await query.get();
            const logs = [];
            
            snapshot.forEach(doc => {
                logs.push({
                    id: doc.id,
                    ...doc.data(),
                    timestamp: doc.data().timestamp?.toDate?.() || doc.data().timestamp
                });
            });
            
            return logs;
        } catch (error) {
            console.error('Error fetching audit logs:', error);
            return [];
        }
    }
    
    async cleanupOldLogs() {
        try {
            if (!this.db) {
                return;
            }
            
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - SECURITY_CONFIG.DATA_RETENTION.AUDIT_LOGS_DAYS);
            
            const oldLogsQuery = this.db.collection('audit_logs')
                .where('timestamp', '<', cutoffDate);
            
            const snapshot = await oldLogsQuery.get();
            
            const batch = this.db.batch();
            snapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            
            await batch.commit();
            
            console.log(`Cleaned up ${snapshot.size} old audit log entries`);
        } catch (error) {
            console.error('Error cleaning up audit logs:', error);
        }
    }
}

// Access control utilities
class AccessControl {
    static isAdminEmail(email) {
        return SECURITY_CONFIG.ADMIN_EMAILS.includes(email);
    }
    
    static hasAdminRole(user) {
        return user && (
            user.admin === true ||
            user.role === 'admin' ||
            this.isAdminEmail(user.email)
        );
    }
    
    static sanitizeUserData(userData) {
        // Remove sensitive fields before sending to client
        const sanitized = { ...userData };
        delete sanitized.password;
        delete sanitized.internalNotes;
        delete sanitized.adminFlags;
        
        return sanitized;
    }
    
    static validateExportRequest(columns, includeAssessments) {
        // Validate that requested columns are allowed
        const allowedColumns = [
            'name', 'email', 'age', 'preferredLanguage', 'hasStudiedBefore',
            'supportNeeds', 'assessmentStatus', 'recommendedCourse',
            'createdAt', 'lastAssessmentDate', 'totalQuestionsAnswered',
            'timeSpent', 'aiSummary', 'confidenceScore'
        ];
        
        const requestedColumns = columns.split(',');
        const invalidColumns = requestedColumns.filter(col => !allowedColumns.includes(col));
        
        if (invalidColumns.length > 0) {
            throw new Error(`Invalid columns requested: ${invalidColumns.join(', ')}`);
        }
        
        // Check if sensitive data is being requested
        const sensitiveColumns = ['supportNeeds', 'aiSummary'];
        const hasSensitiveData = requestedColumns.some(col => sensitiveColumns.includes(col));
        
        return {
            isValid: true,
            hasSensitiveData: hasSensitiveData,
            requestedColumns: requestedColumns
        };
    }
}

// Session management
class SessionManager {
    constructor() {
        this.activeSessions = new Map();
    }
    
    createSession(adminEmail, sessionData) {
        const sessionId = crypto.randomBytes(32).toString('hex');
        const expiresAt = new Date(Date.now() + SECURITY_CONFIG.DATA_RETENTION.SESSION_TIMEOUT_MINUTES * 60 * 1000);
        
        this.activeSessions.set(sessionId, {
            adminEmail: adminEmail,
            createdAt: new Date(),
            expiresAt: expiresAt,
            lastActivity: new Date(),
            ...sessionData
        });
        
        return sessionId;
    }
    
    validateSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        
        if (!session) {
            return null;
        }
        
        if (session.expiresAt < new Date()) {
            this.activeSessions.delete(sessionId);
            return null;
        }
        
        // Update last activity
        session.lastActivity = new Date();
        
        return session;
    }
    
    destroySession(sessionId) {
        this.activeSessions.delete(sessionId);
    }
    
    cleanupExpiredSessions() {
        const now = new Date();
        for (const [sessionId, session] of this.activeSessions.entries()) {
            if (session.expiresAt < now) {
                this.activeSessions.delete(sessionId);
            }
        }
    }
}

module.exports = {
    SECURITY_CONFIG,
    DataEncryption,
    AuditLogger,
    AccessControl,
    SessionManager
};
