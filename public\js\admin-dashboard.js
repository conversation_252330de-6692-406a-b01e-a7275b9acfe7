// Admin Dashboard JavaScript

class AdminDashboard {
    constructor() {
        this.currentUser = null;
        this.studentsData = [];
        this.filteredStudents = [];
        this.currentPage = 1;
        this.itemsPerPage = 50;
        this.sortField = 'createdAt';
        this.sortDirection = 'desc';
        this.filters = {
            search: '',
            status: 'all',
            course: 'all',
            startDate: '',
            endDate: ''
        };

        // Performance optimization
        this.dataCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.loadingStates = new Set();
        this.requestQueue = [];
        this.maxConcurrentRequests = 3;
        this.activeRequests = 0;

        this.init();
    }

    async init() {
        // Check authentication
        await this.checkAuth();
        
        // Initialize event listeners
        this.initEventListeners();
        
        // Load initial data
        await this.loadDashboardData();
        
        // Hide loading overlay
        this.hideLoading();
    }

    async checkAuth() {
        return new Promise((resolve) => {
            auth.onAuthStateChanged(async (user) => {
                if (user) {
                    this.currentUser = user;
                    document.getElementById('adminUserName').textContent = user.email;
                    resolve();
                } else {
                    // Redirect to login
                    window.location.href = '/';
                }
            });
        });
    }

    initEventListeners() {
        // Navigation with keyboard support
        document.querySelectorAll('.nav-item').forEach((item, index) => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.showSection(section);
            });

            // Keyboard navigation
            item.addEventListener('keydown', (e) => {
                this.handleNavKeydown(e, index);
            });
        });

        // Sign out
        document.getElementById('adminSignOutBtn').addEventListener('click', () => {
            this.signOut();
        });

        // Search and filters
        document.getElementById('studentSearch').addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.debounceFilter();
        });

        document.getElementById('statusFilter').addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.applyFilters();
        });

        document.getElementById('courseFilter').addEventListener('change', (e) => {
            this.filters.course = e.target.value;
            this.applyFilters();
        });

        document.getElementById('startDateFilter').addEventListener('change', (e) => {
            this.filters.startDate = e.target.value;
            this.applyFilters();
        });

        document.getElementById('endDateFilter').addEventListener('change', (e) => {
            this.filters.endDate = e.target.value;
            this.applyFilters();
        });

        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearFilters();
        });

        document.getElementById('exportStudentsBtn').addEventListener('click', () => {
            this.showExportModal();
        });

        // Table sorting with keyboard support
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const field = e.currentTarget.dataset.sort;
                this.sortTable(field);
            });

            header.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const field = e.currentTarget.dataset.sort;
                    this.sortTable(field);
                }
            });
        });

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.renderStudentsTable();
            }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
            const totalPages = Math.ceil(this.filteredStudents.length / this.itemsPerPage);
            if (this.currentPage < totalPages) {
                this.currentPage++;
                this.renderStudentsTable();
            }
        });

        // Modal close
        document.getElementById('closeStudentModal').addEventListener('click', () => {
            this.closeStudentModal();
        });

        document.getElementById('closeExportModal').addEventListener('click', () => {
            this.closeExportModal();
        });

        // Export modal controls
        document.getElementById('selectAllColumns').addEventListener('click', () => {
            this.selectAllColumns();
        });

        document.getElementById('selectNoneColumns').addEventListener('click', () => {
            this.selectNoneColumns();
        });

        document.getElementById('selectDefaultColumns').addEventListener('click', () => {
            this.selectDefaultColumns();
        });

        document.getElementById('cancelExport').addEventListener('click', () => {
            this.closeExportModal();
        });

        document.getElementById('confirmExport').addEventListener('click', () => {
            this.performCustomExport();
        });
    }

    showSection(sectionName) {
        // Update navigation with accessibility
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            item.setAttribute('aria-pressed', 'false');
        });

        const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
        activeNavItem.classList.add('active');
        activeNavItem.setAttribute('aria-pressed', 'true');

        // Show section with accessibility
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
            section.setAttribute('aria-hidden', 'true');
        });

        const activeSection = document.getElementById(`${sectionName}Section`);
        activeSection.classList.add('active');
        activeSection.setAttribute('aria-hidden', 'false');

        // Announce section change to screen readers
        this.announceToScreenReader(`Switched to ${sectionName} section`);

        // Focus management
        const sectionHeading = activeSection.querySelector('h2');
        if (sectionHeading) {
            sectionHeading.focus();
        }

        // Load section-specific data
        if (sectionName === 'students' && this.studentsData.length === 0) {
            this.loadStudentsData();
        }
    }

    // Keyboard navigation handler
    handleNavKeydown(event, currentIndex) {
        const navItems = document.querySelectorAll('.nav-item');
        let targetIndex = currentIndex;

        switch (event.key) {
            case 'ArrowLeft':
            case 'ArrowUp':
                event.preventDefault();
                targetIndex = currentIndex > 0 ? currentIndex - 1 : navItems.length - 1;
                break;
            case 'ArrowRight':
            case 'ArrowDown':
                event.preventDefault();
                targetIndex = currentIndex < navItems.length - 1 ? currentIndex + 1 : 0;
                break;
            case 'Home':
                event.preventDefault();
                targetIndex = 0;
                break;
            case 'End':
                event.preventDefault();
                targetIndex = navItems.length - 1;
                break;
            case 'Enter':
            case ' ':
                event.preventDefault();
                const section = event.currentTarget.dataset.section;
                this.showSection(section);
                return;
            default:
                return;
        }

        navItems[targetIndex].focus();
    }

    // Screen reader announcements
    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = message;

        document.body.appendChild(announcement);

        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Enhanced error handling with accessibility
    showError(message) {
        // Create error notification with ARIA
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.setAttribute('role', 'alert');
        errorDiv.setAttribute('aria-live', 'assertive');
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(errorDiv);

        // Focus the error for screen readers
        errorDiv.focus();

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    async loadDashboardData() {
        const cacheKey = 'dashboard_data';
        const cachedData = this.getCachedData(cacheKey);

        if (cachedData) {
            this.studentsData = cachedData.users || [];
            this.updateOverviewStats(cachedData.stats || {});
            this.updateRecentActivity();
            this.updateRecommendationsChart();
            return;
        }

        try {
            this.showSectionLoading('overview');

            const token = await this.currentUser.getIdToken();

            // Load users data for overview with optimized query
            const response = await this.makeRequest('/api/admin/users?limit=1000', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.studentsData = data.users || [];
                this.setCachedData(cacheKey, data);
                this.updateOverviewStats(data.stats || {});
                this.updateRecentActivity();
                this.updateRecommendationsChart();
            } else {
                console.error('Failed to load dashboard data');
                this.showError('Failed to load dashboard data');
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('Error loading dashboard data');
        } finally {
            this.hideSectionLoading('overview');
        }
    }

    // Cache management
    getCachedData(key) {
        const cached = this.dataCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.data;
        }
        this.dataCache.delete(key);
        return null;
    }

    setCachedData(key, data) {
        this.dataCache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    clearCache() {
        this.dataCache.clear();
    }

    // Request queue management
    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ url, options, resolve, reject });
            this.processRequestQueue();
        });
    }

    async processRequestQueue() {
        if (this.activeRequests >= this.maxConcurrentRequests || this.requestQueue.length === 0) {
            return;
        }

        const { url, options, resolve, reject } = this.requestQueue.shift();
        this.activeRequests++;

        try {
            const response = await fetch(url, options);
            resolve(response);
        } catch (error) {
            reject(error);
        } finally {
            this.activeRequests--;
            // Process next request in queue
            setTimeout(() => this.processRequestQueue(), 10);
        }
    }

    // Loading state management
    showSectionLoading(section) {
        this.loadingStates.add(section);
        const loadingEl = document.querySelector(`#${section}Section .section-loading`);
        if (loadingEl) {
            loadingEl.style.display = 'flex';
        }
    }

    hideSectionLoading(section) {
        this.loadingStates.delete(section);
        const loadingEl = document.querySelector(`#${section}Section .section-loading`);
        if (loadingEl) {
            loadingEl.style.display = 'none';
        }
    }

    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(errorDiv);

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    updateOverviewStats(stats) {
        document.getElementById('totalStudentsCount').textContent = stats.total || 0;
        document.getElementById('completedCount').textContent = stats.completed || 0;
        document.getElementById('inProgressCount').textContent = stats.inProgress || 0;
        document.getElementById('notStartedCount').textContent = stats.notStarted || 0;

        // Calculate completion rate
        const completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;
        document.getElementById('completedChange').textContent = `${completionRate}% completion rate`;
    }

    updateRecentActivity() {
        const activityContainer = document.getElementById('recentActivity');
        
        if (this.studentsData.length === 0) {
            activityContainer.innerHTML = '<div class="activity-item"><span class="activity-text">No recent activity</span></div>';
            return;
        }

        // Get recent students (last 5)
        const recentStudents = this.studentsData
            .filter(student => student.createdAt)
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        const activityHTML = recentStudents.map(student => {
            const date = new Date(student.createdAt);
            const timeAgo = this.getTimeAgo(date);
            
            return `
                <div class="activity-item">
                    <span class="activity-text">${student.name} registered</span>
                    <span class="activity-time">${timeAgo}</span>
                </div>
            `;
        }).join('');

        activityContainer.innerHTML = activityHTML || '<div class="activity-item"><span class="activity-text">No recent activity</span></div>';
    }

    updateRecommendationsChart() {
        const chartContainer = document.getElementById('recommendationsChart');
        
        // Count course recommendations
        const courseCounts = {};
        this.studentsData.forEach(student => {
            if (student.recommendedCourse) {
                courseCounts[student.recommendedCourse] = (courseCounts[student.recommendedCourse] || 0) + 1;
            }
        });

        if (Object.keys(courseCounts).length === 0) {
            chartContainer.innerHTML = '<div class="chart-loading">No course recommendations yet</div>';
            return;
        }

        // Create simple bar chart
        const chartHTML = Object.entries(courseCounts)
            .sort(([,a], [,b]) => b - a)
            .map(([course, count]) => {
                const percentage = Math.round((count / this.studentsData.length) * 100);
                return `
                    <div class="chart-bar" style="margin-bottom: 0.75rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                            <span style="font-size: 0.875rem; color: #374151;">${course}</span>
                            <span style="font-size: 0.875rem; font-weight: 600; color: #667eea;">${count}</span>
                        </div>
                        <div style="background: #e5e7eb; height: 8px; border-radius: 4px;">
                            <div style="background: #667eea; height: 100%; width: ${percentage}%; border-radius: 4px;"></div>
                        </div>
                    </div>
                `;
            }).join('');

        chartContainer.innerHTML = chartHTML;
    }

    async loadStudentsData() {
        if (this.studentsData.length > 0) {
            this.filteredStudents = [...this.studentsData];
            this.renderStudentsTable();
            return;
        }

        try {
            const token = await this.currentUser.getIdToken();
            const response = await fetch('/api/admin/users?limit=1000', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.studentsData = data.users || [];
                this.filteredStudents = [...this.studentsData];
                this.renderStudentsTable();
            } else {
                console.error('Failed to load students data');
            }
        } catch (error) {
            console.error('Error loading students data:', error);
        }
    }

    applyFilters() {
        this.filteredStudents = this.studentsData.filter(student => {
            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const matchesSearch = 
                    student.name?.toLowerCase().includes(searchTerm) ||
                    student.email?.toLowerCase().includes(searchTerm) ||
                    student.recommendedCourse?.toLowerCase().includes(searchTerm);
                if (!matchesSearch) return false;
            }

            // Status filter
            if (this.filters.status !== 'all' && student.assessmentStatus !== this.filters.status) {
                return false;
            }

            // Course filter
            if (this.filters.course !== 'all' && student.recommendedCourse !== this.filters.course) {
                return false;
            }

            // Date filters
            if (this.filters.startDate) {
                const startDate = new Date(this.filters.startDate);
                const studentDate = new Date(student.createdAt);
                if (studentDate < startDate) return false;
            }

            if (this.filters.endDate) {
                const endDate = new Date(this.filters.endDate);
                endDate.setHours(23, 59, 59, 999);
                const studentDate = new Date(student.createdAt);
                if (studentDate > endDate) return false;
            }

            return true;
        });

        this.currentPage = 1;
        this.renderStudentsTable();
    }

    debounceFilter() {
        clearTimeout(this.filterTimeout);
        this.filterTimeout = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    clearFilters() {
        this.filters = {
            search: '',
            status: 'all',
            course: 'all',
            startDate: '',
            endDate: ''
        };

        // Reset form inputs
        document.getElementById('studentSearch').value = '';
        document.getElementById('statusFilter').value = 'all';
        document.getElementById('courseFilter').value = 'all';
        document.getElementById('startDateFilter').value = '';
        document.getElementById('endDateFilter').value = '';

        this.applyFilters();
    }

    sortTable(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        // Update sort indicators and ARIA attributes
        document.querySelectorAll('.sortable').forEach(header => {
            header.setAttribute('aria-sort', 'none');
            const indicator = header.querySelector('.sort-indicator');
            indicator.className = 'sort-indicator';
        });

        const currentHeader = document.querySelector(`[data-sort="${field}"]`);
        const currentIndicator = currentHeader.querySelector('.sort-indicator');
        currentIndicator.classList.add(this.sortDirection);
        currentHeader.setAttribute('aria-sort', this.sortDirection === 'asc' ? 'ascending' : 'descending');

        // Announce sort change to screen readers
        const fieldName = currentHeader.textContent.trim().replace(/\s+/g, ' ');
        this.announceToScreenReader(`Table sorted by ${fieldName} in ${this.sortDirection === 'asc' ? 'ascending' : 'descending'} order`);

        // Sort data
        this.filteredStudents.sort((a, b) => {
            let aVal = a[field] || '';
            let bVal = b[field] || '';

            // Handle dates
            if (field === 'createdAt' || field === 'lastAssessmentDate') {
                aVal = aVal ? new Date(aVal) : new Date(0);
                bVal = bVal ? new Date(bVal) : new Date(0);
            }

            // Handle strings
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (this.sortDirection === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });

        this.renderStudentsTable();
    }

    renderStudentsTable() {
        const tbody = document.getElementById('studentsTableBody');
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageStudents = this.filteredStudents.slice(startIndex, endIndex);

        // Performance optimization: use DocumentFragment for batch DOM updates
        const fragment = document.createDocumentFragment();

        if (pageStudents.length === 0) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="7" style="text-align: center; padding: 2rem; color: #64748b;">
                    No students found matching your criteria
                </td>
            `;
            fragment.appendChild(emptyRow);
        } else {
            // Use requestAnimationFrame for smooth rendering
            this.renderTableRows(pageStudents, fragment);
        }

        // Clear existing content and append new rows
        tbody.innerHTML = '';
        tbody.appendChild(fragment);

        this.updatePagination();
    }

    renderTableRows(students, fragment) {
        // Batch process rows to avoid blocking the UI
        const batchSize = 10;
        let currentIndex = 0;

        const processBatch = () => {
            const endIndex = Math.min(currentIndex + batchSize, students.length);

            for (let i = currentIndex; i < endIndex; i++) {
                const student = students[i];
                const row = this.createStudentRow(student);
                fragment.appendChild(row);
            }

            currentIndex = endIndex;

            if (currentIndex < students.length) {
                // Process next batch on next frame
                requestAnimationFrame(processBatch);
            }
        };

        processBatch();
    }

    createStudentRow(student) {
        const row = document.createElement('tr');
        row.setAttribute('role', 'row');

        const createdDate = student.createdAt ? new Date(student.createdAt).toLocaleDateString() : 'N/A';
        const completionDate = student.lastAssessmentDate ?
            new Date(student.lastAssessmentDate).toLocaleDateString() :
            (student.assessmentStatus === 'completed' ? 'Completed' : 'In Progress');

        const statusClass = `status-${student.assessmentStatus?.replace('_', '-') || 'not-started'}`;
        const statusText = student.assessmentStatus?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not Started';

        row.innerHTML = `
            <td role="cell">${this.escapeHtml(student.name || 'N/A')}</td>
            <td role="cell">${this.escapeHtml(student.email || 'N/A')}</td>
            <td role="cell">${createdDate}</td>
            <td role="cell">${completionDate}</td>
            <td role="cell">
                <span class="status-badge ${statusClass}" aria-label="Assessment status: ${statusText}">
                    ${statusText}
                </span>
            </td>
            <td role="cell">
                ${student.recommendedCourse ?
                    `<a href="#" class="course-link" onclick="adminDashboard.showCourseDetails('${this.escapeHtml(student.recommendedCourse)}')" aria-label="View course details for ${this.escapeHtml(student.recommendedCourse)}">${this.escapeHtml(student.recommendedCourse)}</a>` :
                    '<span aria-label="No course assigned">Not assigned</span>'
                }
            </td>
            <td role="cell">
                <button class="action-btn primary" onclick="adminDashboard.viewStudentDetails('${this.escapeHtml(student.email)}')" aria-label="View details for ${this.escapeHtml(student.name || student.email)}">View</button>
                <button class="action-btn" onclick="adminDashboard.exportStudent('${this.escapeHtml(student.email)}')" aria-label="Export data for ${this.escapeHtml(student.name || student.email)}">Export</button>
            </td>
        `;

        return row;
    }

    // Security: Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Optimized filtering with debouncing
    applyFiltersOptimized() {
        // Cancel previous filter operation if still running
        if (this.filterTimeout) {
            clearTimeout(this.filterTimeout);
        }

        this.filterTimeout = setTimeout(() => {
            this.performFiltering();
        }, 150); // Reduced debounce time for better responsiveness
    }

    performFiltering() {
        const startTime = performance.now();

        // Use more efficient filtering for large datasets
        this.filteredStudents = this.studentsData.filter(student => {
            // Early return for better performance
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchableText = `${student.name || ''} ${student.email || ''} ${student.recommendedCourse || ''}`.toLowerCase();
                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }

            if (this.filters.status !== 'all' && student.assessmentStatus !== this.filters.status) {
                return false;
            }

            if (this.filters.course !== 'all' && student.recommendedCourse !== this.filters.course) {
                return false;
            }

            // Date filtering with optimized date comparison
            if (this.filters.startDate || this.filters.endDate) {
                const studentDate = student.createdAt ? new Date(student.createdAt) : null;
                if (!studentDate) return false;

                if (this.filters.startDate && studentDate < new Date(this.filters.startDate)) {
                    return false;
                }

                if (this.filters.endDate) {
                    const endDate = new Date(this.filters.endDate);
                    endDate.setHours(23, 59, 59, 999);
                    if (studentDate > endDate) {
                        return false;
                    }
                }
            }

            return true;
        });

        this.currentPage = 1;
        this.renderStudentsTable();

        const endTime = performance.now();
        console.log(`Filtering completed in ${endTime - startTime} milliseconds`);
    }

    updatePagination() {
        const totalPages = Math.ceil(this.filteredStudents.length / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.filteredStudents.length);

        // Update pagination info
        document.getElementById('paginationInfo').textContent = 
            `Showing ${startItem}-${endItem} of ${this.filteredStudents.length} students`;

        // Update pagination buttons
        document.getElementById('prevPageBtn').disabled = this.currentPage === 1;
        document.getElementById('nextPageBtn').disabled = this.currentPage === totalPages || totalPages === 0;

        // Update page numbers
        const pageNumbersContainer = document.getElementById('pageNumbers');
        const pageNumbers = [];
        
        for (let i = Math.max(1, this.currentPage - 2); i <= Math.min(totalPages, this.currentPage + 2); i++) {
            pageNumbers.push(`
                <button class="page-number ${i === this.currentPage ? 'active' : ''}" 
                        onclick="adminDashboard.goToPage(${i})">${i}</button>
            `);
        }

        pageNumbersContainer.innerHTML = pageNumbers.join('');
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderStudentsTable();
    }

    async viewStudentDetails(email) {
        const modal = document.getElementById('studentDetailModal');
        const content = document.getElementById('studentDetailContent');
        
        // Show modal with loading
        content.innerHTML = `
            <div class="detail-loading">
                <div class="spinner"></div>
                <span>Loading student details...</span>
            </div>
        `;
        modal.style.display = 'flex';

        try {
            const token = await this.currentUser.getIdToken();
            const response = await fetch(`/api/admin/student/${encodeURIComponent(email)}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const studentData = await response.json();
                this.renderStudentDetails(studentData);
            } else {
                content.innerHTML = '<div class="error">Failed to load student details</div>';
            }
        } catch (error) {
            console.error('Error loading student details:', error);
            content.innerHTML = '<div class="error">Error loading student details</div>';
        }
    }

    renderStudentDetails(studentData) {
        const content = document.getElementById('studentDetailContent');
        const profile = studentData.profile;
        const assessments = studentData.assessments || [];

        const detailsHTML = `
            <div class="student-detail-sections">
                <div class="detail-section">
                    <h4>Student Profile</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Full Name:</label>
                            <span>${profile.name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Email:</label>
                            <span>${profile.email || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Age:</label>
                            <span>${profile.age || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Preferred Language:</label>
                            <span>${profile.preferredLanguage || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Previously Studied:</label>
                            <span>${profile.hasStudiedBefore ? 'Yes' : 'No'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Support Needs:</label>
                            <span>${profile.supportNeeds || 'None specified'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Registration Date:</label>
                            <span>${profile.createdAt ? new Date(profile.createdAt).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Assessment Status:</label>
                            <span class="status-badge status-${profile.assessmentStatus?.replace('_', '-') || 'not-started'}">
                                ${profile.assessmentStatus?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not Started'}
                            </span>
                        </div>
                    </div>
                </div>

                ${assessments.length > 0 ? `
                    <div class="detail-section">
                        <h4>Assessment History</h4>
                        <div class="assessments-list">
                            ${assessments.map((assessment, index) => `
                                <div class="assessment-item">
                                    <div class="assessment-header">
                                        <strong>Assessment #${index + 1} - ${assessment.completedAt ? new Date(assessment.completedAt).toLocaleDateString() : 'In Progress'}</strong>
                                        <span class="assessment-score">Score: ${assessment.totalQuestionsAnswered || 0} questions answered</span>
                                    </div>

                                    <div class="assessment-details">
                                        <div class="assessment-summary">
                                            <p><strong>Recommended Course:</strong> ${assessment.finalRecommendation || 'Not determined'}</p>
                                            <p><strong>Time Spent:</strong> ${assessment.timeSpent || 0} minutes</p>
                                            <p><strong>Started:</strong> ${assessment.createdAt ? new Date(assessment.createdAt).toLocaleDateString() : 'N/A'}</p>
                                        </div>

                                        ${assessment.aiAnalysis ? `
                                            <div class="ai-analysis-section">
                                                <h5>AI Analysis</h5>
                                                <div class="ai-analysis-content">
                                                    <p><strong>Summary:</strong> ${assessment.aiAnalysis.summary}</p>
                                                    <p><strong>Recommendation:</strong> ${assessment.aiAnalysis.recommendation}</p>
                                                    ${assessment.aiAnalysis.confidence !== 'Not available' ? `<p><strong>Confidence:</strong> ${assessment.aiAnalysis.confidence}</p>` : ''}
                                                    ${assessment.aiAnalysis.reasoning !== 'No reasoning provided' ? `<p><strong>Reasoning:</strong> ${assessment.aiAnalysis.reasoning}</p>` : ''}
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${assessment.sectionScores ? `
                                            <div class="section-scores">
                                                <h5>Section Performance</h5>
                                                <div class="scores-grid">
                                                    ${Object.entries(assessment.sectionScores).map(([section, score]) => `
                                                        <div class="score-item">
                                                            <span class="section-name">${section}</span>
                                                            <span class="score-value">${score.correct}/${score.total}</span>
                                                            <div class="score-bar">
                                                                <div class="score-fill" style="width: ${score.total > 0 ? (score.correct / score.total) * 100 : 0}%"></div>
                                                            </div>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${assessment.responsesBySection ? `
                                            <div class="responses-section">
                                                <h5>Detailed Responses</h5>
                                                <div class="responses-accordion">
                                                    ${Object.entries(assessment.responsesBySection).map(([section, responses]) => `
                                                        <div class="accordion-item">
                                                            <button class="accordion-header" onclick="this.parentElement.classList.toggle('active')">
                                                                ${section} (${responses.length} questions)
                                                                <span class="accordion-icon">▼</span>
                                                            </button>
                                                            <div class="accordion-content">
                                                                ${responses.map(response => `
                                                                    <div class="response-item">
                                                                        <div class="question-text">${response.question}</div>
                                                                        <div class="answer-text ${response.answer === 'yes' ? 'answer-yes' : 'answer-no'}">
                                                                            ${response.answer === 'yes' ? '✅ Yes, I can do this' : '❌ No, I cannot do this'}
                                                                        </div>
                                                                    </div>
                                                                `).join('')}
                                                            </div>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="detail-actions">
                    <button class="btn btn-primary" onclick="adminDashboard.exportStudent('${profile.email}')">
                        Export Student Data
                    </button>
                    <button class="btn btn-outline" onclick="adminDashboard.generatePDFReport('${profile.email}')">
                        Generate PDF Report
                    </button>
                    <button class="btn btn-outline" onclick="adminDashboard.closeStudentModal()">
                        Close
                    </button>
                </div>
            </div>
        `;

        content.innerHTML = detailsHTML;
    }

    closeStudentModal() {
        document.getElementById('studentDetailModal').style.display = 'none';
    }

    showExportModal() {
        document.getElementById('exportModal').style.display = 'flex';
        this.selectDefaultColumns();
    }

    closeExportModal() {
        document.getElementById('exportModal').style.display = 'none';
    }

    selectAllColumns() {
        document.querySelectorAll('#exportModal input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    selectNoneColumns() {
        document.querySelectorAll('#exportModal input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    selectDefaultColumns() {
        // Reset all checkboxes
        this.selectNoneColumns();

        // Select default columns
        const defaultColumns = ['name', 'email', 'assessmentStatus', 'recommendedCourse', 'createdAt'];
        defaultColumns.forEach(column => {
            const checkbox = document.querySelector(`#exportModal input[value="${column}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }

    async performCustomExport() {
        try {
            // Get selected format
            const format = document.querySelector('input[name="exportFormat"]:checked').value;

            // Get selected columns
            const selectedColumns = [];
            document.querySelectorAll('#exportModal input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value) {
                    selectedColumns.push(checkbox.value);
                }
            });

            if (selectedColumns.length === 0) {
                alert('Please select at least one column to export.');
                return;
            }

            // Get include assessments option
            const includeAssessments = document.getElementById('includeAssessments').checked;

            // Build export URL
            const params = new URLSearchParams({
                format: format,
                columns: selectedColumns.join(','),
                includeAssessments: includeAssessments.toString()
            });

            const token = await this.currentUser.getIdToken();
            const response = await fetch(`/api/admin/export/students?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                if (format === 'csv') {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `students_export_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                } else {
                    const data = await response.json();
                    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `students_export_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }

                this.closeExportModal();
                this.showSuccessMessage('Data exported successfully!');
            } else {
                alert('Failed to export data');
            }
        } catch (error) {
            console.error('Export error:', error);
            alert('Error exporting data');
        }
    }

    showSuccessMessage(message) {
        // Create and show a temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification';
        successDiv.textContent = message;
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            successDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 300);
        }, 3000);
    }

    async exportStudent(email) {
        try {
            const token = await this.currentUser.getIdToken();
            const response = await fetch(`/api/admin/export/students?format=csv&student=${encodeURIComponent(email)}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `student_${email.replace('@', '_')}_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            } else {
                alert('Failed to export student data');
            }
        } catch (error) {
            console.error('Export error:', error);
            alert('Error exporting student data');
        }
    }

    async generatePDFReport(email) {
        try {
            // Get student details
            const token = await this.currentUser.getIdToken();
            const response = await fetch(`/api/admin/student/${encodeURIComponent(email)}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const studentData = await response.json();
                this.createPDFReport(studentData);
            } else {
                alert('Failed to generate PDF report');
            }
        } catch (error) {
            console.error('PDF generation error:', error);
            alert('Error generating PDF report');
        }
    }

    createPDFReport(studentData) {
        const profile = studentData.profile;
        const assessments = studentData.assessments || [];

        // Create a new window for the PDF content
        const printWindow = window.open('', '_blank');

        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Student Assessment Report - ${profile.name}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                    .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                    .section { margin-bottom: 30px; }
                    .section h2 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 10px; }
                    .profile-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
                    .profile-item { padding: 10px; background: #f9f9f9; border-radius: 5px; }
                    .profile-item strong { color: #555; }
                    .assessment-item { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
                    .assessment-header { background: #f0f0f0; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
                    .score-item { display: flex; justify-content: space-between; padding: 8px; background: #f9f9f9; margin: 5px 0; border-radius: 4px; }
                    .response-section { margin-top: 20px; }
                    .response-item { padding: 10px; border-left: 3px solid #ddd; margin: 10px 0; }
                    .answer-yes { border-left-color: #10b981; background: #f0fdf4; }
                    .answer-no { border-left-color: #ef4444; background: #fef2f2; }
                    @media print { body { margin: 0; } .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Digital Skills Assessment Report</h1>
                    <p>Generated on ${new Date().toLocaleDateString()}</p>
                </div>

                <div class="section">
                    <h2>Student Profile</h2>
                    <div class="profile-grid">
                        <div class="profile-item"><strong>Name:</strong> ${profile.name || 'N/A'}</div>
                        <div class="profile-item"><strong>Email:</strong> ${profile.email || 'N/A'}</div>
                        <div class="profile-item"><strong>Age:</strong> ${profile.age || 'N/A'}</div>
                        <div class="profile-item"><strong>Language:</strong> ${profile.preferredLanguage || 'N/A'}</div>
                        <div class="profile-item"><strong>Previously Studied:</strong> ${profile.hasStudiedBefore ? 'Yes' : 'No'}</div>
                        <div class="profile-item"><strong>Registration:</strong> ${profile.createdAt ? new Date(profile.createdAt).toLocaleDateString() : 'N/A'}</div>
                    </div>
                    ${profile.supportNeeds ? `<div class="profile-item"><strong>Support Needs:</strong> ${profile.supportNeeds}</div>` : ''}
                </div>

                ${assessments.length > 0 ? assessments.map((assessment, index) => `
                    <div class="section">
                        <h2>Assessment #${index + 1}</h2>
                        <div class="assessment-item">
                            <div class="assessment-header">
                                <strong>Completed:</strong> ${assessment.completedAt ? new Date(assessment.completedAt).toLocaleDateString() : 'In Progress'}<br>
                                <strong>Questions Answered:</strong> ${assessment.totalQuestionsAnswered || 0}<br>
                                <strong>Time Spent:</strong> ${assessment.timeSpent || 0} minutes<br>
                                <strong>Recommended Course:</strong> ${assessment.finalRecommendation || 'Not determined'}
                            </div>

                            ${assessment.aiAnalysis ? `
                                <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                    <h3 style="margin-top: 0; color: #0c4a6e;">AI Analysis</h3>
                                    <p><strong>Summary:</strong> ${assessment.aiAnalysis.summary}</p>
                                    <p><strong>Recommendation:</strong> ${assessment.aiAnalysis.recommendation}</p>
                                    ${assessment.aiAnalysis.reasoning !== 'No reasoning provided' ? `<p><strong>Reasoning:</strong> ${assessment.aiAnalysis.reasoning}</p>` : ''}
                                </div>
                            ` : ''}

                            ${assessment.sectionScores ? `
                                <div>
                                    <h3>Section Performance</h3>
                                    ${Object.entries(assessment.sectionScores).map(([section, score]) => `
                                        <div class="score-item">
                                            <span>${section}</span>
                                            <span><strong>${score.correct}/${score.total}</strong> (${score.total > 0 ? Math.round((score.correct / score.total) * 100) : 0}%)</span>
                                        </div>
                                    `).join('')}
                                </div>
                            ` : ''}

                            ${assessment.responsesBySection ? `
                                <div class="response-section">
                                    <h3>Detailed Responses</h3>
                                    ${Object.entries(assessment.responsesBySection).map(([section, responses]) => `
                                        <div>
                                            <h4>${section}</h4>
                                            ${responses.map(response => `
                                                <div class="response-item ${response.answer === 'yes' ? 'answer-yes' : 'answer-no'}">
                                                    <div><strong>Q:</strong> ${response.question}</div>
                                                    <div><strong>A:</strong> ${response.answer === 'yes' ? 'Yes, I can do this' : 'No, I cannot do this'}</div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    `).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('') : '<div class="section"><p>No assessment data available.</p></div>'}

                <div class="no-print" style="text-align: center; margin-top: 30px;">
                    <button onclick="window.print()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">Print Report</button>
                    <button onclick="window.close()" style="padding: 10px 20px; background: #6b7280; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">Close</button>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(htmlContent);
        printWindow.document.close();
    }

    async exportStudents() {
        try {
            const token = await this.currentUser.getIdToken();
            const response = await fetch('/api/admin/export/students?format=csv', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `students_export_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            } else {
                alert('Failed to export students data');
            }
        } catch (error) {
            console.error('Export error:', error);
            alert('Error exporting students data');
        }
    }

    showCourseDetails(courseName) {
        alert(`Course Details: ${courseName}\n\nDetailed course information will be available in a future update.`);
    }

    async signOut() {
        try {
            await auth.signOut();
            window.location.href = '/';
        } catch (error) {
            console.error('Sign out error:', error);
        }
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        
        return date.toLocaleDateString();
    }

    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});

// Handle modal clicks outside content
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('modal-overlay')) {
        e.target.style.display = 'none';
    }
});
