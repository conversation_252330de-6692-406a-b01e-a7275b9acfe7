/* Admin Dashboard Styles */

/* Admin Body Layout */
.admin-body {
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Admin Header */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.admin-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-logo h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.admin-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    margin-left: 1rem;
    backdrop-filter: blur(10px);
}

.admin-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-user-name {
    font-weight: 500;
}

/* Admin Navigation */
.admin-nav {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 0;
    position: sticky;
    top: 80px;
    z-index: 90;
}

.nav-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    gap: 0;
}

.nav-item {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
    color: #64748b;
    font-weight: 500;
}

.nav-item:hover {
    background-color: #f1f5f9;
    color: #334155;
}

.nav-item.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: #f8fafc;
}

.nav-icon {
    font-size: 1.1rem;
}

.nav-text {
    font-size: 0.9rem;
}

/* Main Content */
.admin-main {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    box-sizing: border-box;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.75rem;
    font-weight: 600;
}

.section-header p {
    margin: 0;
    color: #64748b;
    font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
}

.stat-content h3 {
    margin: 0 0 0.25rem 0;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.75rem;
    color: #64748b;
}

.stat-change.positive {
    color: #059669;
}

.stat-change.negative {
    color: #dc2626;
}

/* Overview Charts */
.overview-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.chart-card h3 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.activity-time {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}

.activity-text {
    font-size: 0.875rem;
    color: #334155;
}

/* Filters Container */
.filters-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.search-box {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    padding: 0.75rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-btn:hover {
    background: #5a67d8;
}

.filters-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-select,
.filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    background: white;
    min-width: 150px;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.admin-table th {
    background: #f8fafc;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 10;
}

.admin-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.admin-table th.sortable:hover {
    background: #f1f5f9;
}

.sort-indicator {
    margin-left: 0.5rem;
    opacity: 0.5;
}

.sort-indicator.asc::after {
    content: '↑';
    opacity: 1;
}

.sort-indicator.desc::after {
    content: '↓';
    opacity: 1;
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
}

.admin-table tbody tr:hover {
    background: #f9fafb;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-in-progress {
    background: #fef3c7;
    color: #92400e;
}

.status-not-started {
    background: #e5e7eb;
    color: #374151;
}

.course-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
}

.course-link:hover {
    text-decoration: underline;
}

.action-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
}

.action-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.action-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.action-btn.primary:hover {
    background: #5a67d8;
    border-color: #5a67d8;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.pagination-info {
    color: #64748b;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-numbers {
    display: flex;
    gap: 0.25rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-number:hover {
    background: #f3f4f6;
}

.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Modal Styles */
.modal-large .modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

/* Student Detail Modal */
.student-detail-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.detail-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.detail-section h4 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.detail-item span {
    color: #64748b;
    font-size: 0.875rem;
}

.assessments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.assessment-item {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e2e8f0;
}

.assessment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.assessment-score {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.assessment-details p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    color: #64748b;
}

.assessment-details strong {
    color: #374151;
}

.detail-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.error {
    text-align: center;
    padding: 2rem;
    color: #dc2626;
    background: #fef2f2;
    border-radius: 8px;
    border: 1px solid #fecaca;
}

/* Enhanced Assessment Details */
.ai-analysis-section {
    background: #f0f9ff;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid #0ea5e9;
}

.ai-analysis-section h5 {
    margin: 0 0 0.75rem 0;
    color: #0c4a6e;
    font-size: 1rem;
    font-weight: 600;
}

.ai-analysis-content p {
    margin: 0.5rem 0;
    font-size: 0.875rem;
    line-height: 1.5;
}

.section-scores {
    margin: 1rem 0;
}

.section-scores h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.scores-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.score-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.section-name {
    flex: 1;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.score-value {
    font-weight: 600;
    color: #667eea;
    min-width: 40px;
    text-align: center;
}

.score-bar {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    max-width: 100px;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.3s ease;
}

.responses-section {
    margin: 1rem 0;
}

.responses-section h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.responses-accordion {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid #e5e7eb;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    width: 100%;
    padding: 1rem;
    background: #f9fafb;
    border: none;
    text-align: left;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s ease;
}

.accordion-header:hover {
    background: #f3f4f6;
}

.accordion-icon {
    transition: transform 0.2s ease;
}

.accordion-item.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-item.active .accordion-content {
    max-height: 1000px;
}

.response-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.response-item:last-child {
    border-bottom: none;
}

.question-text {
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.answer-text {
    font-size: 0.8125rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.answer-yes {
    background: #d1fae5;
    color: #065f46;
}

.answer-no {
    background: #fee2e2;
    color: #991b1b;
}

.assessment-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6;
}

/* Export Modal Styles */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.export-section {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
}

.export-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.export-section h4 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.export-section h5 {
    margin: 0 0 0.5rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.radio-group,
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.radio-label,
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.radio-label:hover,
.checkbox-label:hover {
    background-color: #f9fafb;
}

.radio-label input[type="radio"],
.checkbox-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.radio-label span,
.checkbox-label span {
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
}

.columns-selection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.column-group {
    background: #f9fafb;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e5e7eb;
}

.column-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Loading States */
.table-loading,
.detail-loading,
.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    color: #64748b;
}

.spinner-sm {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Coming Soon */
.coming-soon {
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
}

.coming-soon h3 {
    margin: 0 0 1rem 0;
    color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .nav-content {
        padding: 0 1rem;
        overflow-x: auto;
    }
    
    .nav-item {
        white-space: nowrap;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .overview-charts {
        grid-template-columns: 1fr;
    }
    
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-select,
    .filter-input {
        min-width: auto;
    }
    
    .admin-table {
        font-size: 0.75rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .admin-logo h1 {
        font-size: 1.25rem;
    }
    
    .admin-badge {
        margin-left: 0.5rem;
        font-size: 0.625rem;
    }
    
    .nav-text {
        display: none;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .admin-table {
        min-width: 600px;
    }
}
