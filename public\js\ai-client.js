// Client-side AI Assistant Service
class AIClientService {
  constructor() {
    this.isEnabled = true;
    this.cache = new Map();
    this.interactions = [];
  }

  // Check if AI service is available
  async checkHealth() {
    try {
      const response = await fetch('/api/ai/health');
      const data = await response.json();
      this.isEnabled = data.healthy;
      return data.healthy;
    } catch (error) {
      console.warn('AI service health check failed:', error);
      this.isEnabled = false;
      return false;
    }
  }

  // Generic AI API call with error handling
  async callAI(endpoint, data) {
    if (!this.isEnabled) {
      throw new Error('AI service is disabled');
    }

    try {
      const token = appCurrentUser ? await appCurrentUser.getIdToken() : null;
      if (!token) {
        throw new Error('User not authenticated');
      }

      const response = await fetch(`/api/ai/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'AI request failed');
      }

      // Store interaction for analytics
      this.interactions.push({
        type: result.type,
        timestamp: new Date().toISOString(),
        success: true
      });

      return result.message;
    } catch (error) {
      console.error(`AI ${endpoint} error:`, error);
      
      // Store failed interaction
      this.interactions.push({
        type: endpoint,
        timestamp: new Date().toISOString(),
        success: false,
        error: error.message
      });

      throw error;
    }
  }

  // Get user profile for AI context
  getUserProfile() {
    // This should be populated from the user's registration data
    return {
      name: appCurrentUser?.displayName || 'Student',
      email: appCurrentUser?.email || '',
      age: null, // Will be fetched from user profile
      preferredLanguage: 'english',
      hasStudiedBefore: false,
      supportNeeds: null
    };
  }

  // Generate welcome message
  async generateWelcomeMessage(userProfile = null) {
    const cacheKey = 'welcome_' + (userProfile?.email || 'default');
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const profile = userProfile || this.getUserProfile();
      const message = await this.callAI('welcome', { userProfile: profile });
      
      this.cache.set(cacheKey, message);
      return message;
    } catch (error) {
      return "Hello! I'm your Digital Skills Assistant. I'm here to help you discover your digital skills level. Please answer honestly - there are no wrong answers!";
    }
  }

  // Generate section introduction
  async generateSectionIntro(sectionNumber, previousPerformance = null) {
    try {
      const userProfile = this.getUserProfile();
      const message = await this.callAI('section-intro', {
        sectionNumber,
        userProfile,
        previousPerformance
      });
      
      return message;
    } catch (error) {
      const sectionNames = {
        1: 'Basic IT Skills',
        2: 'Intermediate Digital Skills', 
        3: 'Confident User Skills'
      };
      return `Well done! Ready to continue with ${sectionNames[sectionNumber]}?`;
    }
  }

  // Generate encouragement message
  async generateEncouragement(currentProgress, performancePattern = null) {
    try {
      const userProfile = this.getUserProfile();
      const message = await this.callAI('encouragement', {
        currentProgress,
        userProfile,
        performancePattern
      });
      
      return message;
    } catch (error) {
      return "You're doing great! Keep going - every question helps us understand your skills better.";
    }
  }

  // Clarify question for user
  async clarifyQuestion(questionText) {
    try {
      const userProfile = this.getUserProfile();
      const message = await this.callAI('clarify', {
        questionText,
        userProfile
      });
      
      return message;
    } catch (error) {
      return "This question is asking about your ability to perform a specific digital task. Answer 'Yes' if you can do it confidently, or 'No' if you're not sure or haven't done it before.";
    }
  }

  // Generate assessment feedback
  async generateFeedback(assessmentResults) {
    try {
      const userProfile = this.getUserProfile();
      const message = await this.callAI('feedback', {
        assessmentResults,
        userProfile
      });
      
      return message;
    } catch (error) {
      return "Thank you for completing the assessment! Based on your responses, we've identified the perfect course to help you develop your digital skills further.";
    }
  }

  // Answer user questions
  async answerUserQuestion(userQuestion, assessmentContext = {}) {
    try {
      const userProfile = this.getUserProfile();
      const message = await this.callAI('question', {
        userQuestion,
        userProfile,
        assessmentContext
      });
      
      return message;
    } catch (error) {
      return "I'm here to help! Feel free to continue with your assessment, and remember there are no wrong answers.";
    }
  }

  // Get interaction analytics
  getAnalytics() {
    const total = this.interactions.length;
    const successful = this.interactions.filter(i => i.success).length;
    const failed = total - successful;
    
    const typeStats = {};
    this.interactions.forEach(interaction => {
      typeStats[interaction.type] = (typeStats[interaction.type] || 0) + 1;
    });

    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total * 100).toFixed(1) : 0,
      typeStats,
      isEnabled: this.isEnabled
    };
  }

  // Clear cache and interactions
  reset() {
    this.cache.clear();
    this.interactions = [];
  }
}

// Create global instance
const aiClient = new AIClientService();

// Initialize AI service when page loads
document.addEventListener('DOMContentLoaded', async () => {
  await aiClient.checkHealth();
  console.log('AI Client Service initialized. Health:', aiClient.isEnabled);
});

// Make available globally
window.aiClient = aiClient;
